// Simple test script to verify delivery date sorting without complex indexes
import { initializeApp } from 'firebase/app';
import { getFirestore, collection, query, orderBy, limit, getDocs } from 'firebase/firestore';

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyCJ11B1BATse018MOQ1NTKQY45zY6fg-SU",
  authDomain: "tailor-dd259.firebaseapp.com",
  projectId: "tailor-dd259",
  storageBucket: "tailor-dd259.appspot.com",
  messagingSenderId: "847981940747",
  appId: "1:847981940747:android:eb82f08a16c340f5f0b70e"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

const MEASUREMENTS_COLLECTION = 'measurements';

// Helper function to parse date and create timestamp for sorting
const createTimestamp = (dateStr) => {
  if (!dateStr) return Date.now(); // Use current timestamp if no date

  // Handle both DD-MM-YYYY and YYYY-MM-DD formats
  let parsedDate;
  if (dateStr.includes('-')) {
    const parts = dateStr.split('-');
    if (parts[0].length === 4) {
      // YYYY-MM-DD format
      parsedDate = new Date(parseInt(parts[0]), parseInt(parts[1]) - 1, parseInt(parts[2]));
    } else {
      // DD-MM-YYYY format
      parsedDate = new Date(parseInt(parts[2]), parseInt(parts[1]) - 1, parseInt(parts[0]));
    }
  } else {
    parsedDate = new Date(dateStr);
  }

  return parsedDate.getTime();
};

// Test simple delivery date sorting
async function testSimpleDeliverySort() {
  try {
    console.log('🧪 Testing simple delivery date sorting...');
    
    const measurementsRef = collection(db, MEASUREMENTS_COLLECTION);
    
    // Create timestamp for start of 2025 (January 1, 2025)
    const start2025 = new Date('2025-01-01').getTime();
    // Create timestamp for end of 2025 (December 31, 2025)
    const end2025 = new Date('2025-12-31T23:59:59').getTime();
    
    // Test simple query with delivery date sorting (no complex where clauses)
    const testQuery = query(
      measurementsRef,
      orderBy('deliveryTimestamp', 'desc'),
      limit(30) // Get more to filter client-side
    );

    const querySnapshot = await getDocs(testQuery);
    
    console.log(`✅ Simple delivery date sorting test successful!`);
    console.log(`📊 Retrieved ${querySnapshot.size} measurements`);
    
    // Filter for 2025 data client-side
    const measurements2025 = [];
    querySnapshot.docs.forEach((doc) => {
      const data = doc.data();
      const measurementTimestamp = data.timestamp || createTimestamp(data.date);
      if (measurementTimestamp >= start2025 && measurementTimestamp <= end2025) {
        measurements2025.push({
          id: doc.id,
          name: data.name || 'Unknown',
          deliveryDate: data.deliveryDate || 'N/A',
          deliveryTimestamp: data.deliveryTimestamp,
          date: data.date
        });
      }
    });
    
    console.log(`📊 Found ${measurements2025.length} measurements from 2025`);
    
    if (measurements2025.length > 0) {
      console.log('\n📝 Top 2025 measurements by latest delivery date:');
      measurements2025.slice(0, 10).forEach((measurement, index) => {
        const deliveryDate = new Date(measurement.deliveryTimestamp).toLocaleDateString();
        console.log(`${index + 1}. ${measurement.name} - Delivery: ${measurement.deliveryDate} (${deliveryDate})`);
      });
    } else {
      console.log('📭 No 2025 measurements found');
    }
    
  } catch (error) {
    console.error('❌ Simple delivery date sorting test failed:', error);
  }
}

// Run the test
testSimpleDeliverySort().then(() => {
  console.log('🏁 Simple delivery date sorting test completed');
  process.exit(0);
}).catch((error) => {
  console.error('💥 Simple delivery date sorting test failed:', error);
  process.exit(1);
});
