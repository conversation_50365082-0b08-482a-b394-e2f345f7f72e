// Script to add timestamps to existing measurements that don't have them
import { initializeApp } from 'firebase/app';
import { getFirestore, collection, getDocs, updateDoc, doc, query, where } from 'firebase/firestore';

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyCJ11B1BATse018MOQ1NTKQY45zY6fg-SU",
  authDomain: "tailor-dd259.firebaseapp.com",
  projectId: "tailor-dd259",
  storageBucket: "tailor-dd259.appspot.com",
  messagingSenderId: "847981940747",
  appId: "1:847981940747:android:eb82f08a16c340f5f0b70e"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

const MEASUREMENTS_COLLECTION = 'measurements';

// Helper function to parse date and create timestamp for sorting
const createTimestamp = (dateStr) => {
  if (!dateStr) return Date.now(); // Use current timestamp if no date

  // Handle both DD-MM-YYYY and YYYY-MM-DD formats
  let parsedDate;
  if (dateStr.includes('-')) {
    const parts = dateStr.split('-');
    if (parts[0].length === 4) {
      // YYYY-MM-DD format
      parsedDate = new Date(parseInt(parts[0]), parseInt(parts[1]) - 1, parseInt(parts[2]));
    } else {
      // DD-MM-YYYY format
      parsedDate = new Date(parseInt(parts[2]), parseInt(parts[1]) - 1, parseInt(parts[0]));
    }
  } else {
    parsedDate = new Date(dateStr);
  }

  return parsedDate.getTime();
};

// Fix timestamps for existing measurements
async function fixTimestamps() {
  try {
    console.log('🔍 Checking measurements for missing timestamps...');
    const measurementsRef = collection(db, MEASUREMENTS_COLLECTION);
    const querySnapshot = await getDocs(measurementsRef);
    
    console.log(`📊 Found ${querySnapshot.size} total measurements`);
    
    let updatedCount = 0;
    let batchCount = 0;
    const batchSize = 100; // Process in batches to avoid overwhelming Firestore
    
    for (const docSnapshot of querySnapshot.docs) {
      const data = docSnapshot.data();
      
      // Check if timestamp is missing or invalid
      if (!data.timestamp || typeof data.timestamp !== 'number') {
        try {
          const timestamp = createTimestamp(data.date);
          
          // Update the document with the timestamp
          await updateDoc(doc(db, MEASUREMENTS_COLLECTION, docSnapshot.id), {
            timestamp: timestamp
          });
          
          updatedCount++;
          console.log(`✅ Updated ${docSnapshot.id} with timestamp: ${timestamp} (${new Date(timestamp).toLocaleDateString()})`);
          
          // Add a small delay every batch to avoid rate limiting
          batchCount++;
          if (batchCount >= batchSize) {
            console.log(`⏸️  Processed ${updatedCount} documents, pausing briefly...`);
            await new Promise(resolve => setTimeout(resolve, 1000)); // 1 second pause
            batchCount = 0;
          }
        } catch (error) {
          console.error(`❌ Error updating ${docSnapshot.id}:`, error);
        }
      }
    }
    
    console.log(`🎉 Timestamp fix completed! Updated ${updatedCount} measurements.`);
    
    // Test the query to make sure it works now
    console.log('🧪 Testing orderBy timestamp query...');
    const testQuery = query(measurementsRef);
    const testSnapshot = await getDocs(testQuery);
    console.log(`✅ Query test successful! Found ${testSnapshot.size} measurements.`);
    
  } catch (error) {
    console.error('❌ Error fixing timestamps:', error);
  }
}

// Run the fix
fixTimestamps().then(() => {
  console.log('🏁 Timestamp fix script completed');
  process.exit(0);
}).catch((error) => {
  console.error('💥 Timestamp fix script failed:', error);
  process.exit(1);
});
