// Test script to verify search functionality across all measurements
import { initializeApp } from 'firebase/app';
import { getFirestore, collection, getDocs } from 'firebase/firestore';

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyCJ11B1BATse018MOQ1NTKQY45zY6fg-SU",
  authDomain: "tailor-dd259.firebaseapp.com",
  projectId: "tailor-dd259",
  storageBucket: "tailor-dd259.appspot.com",
  messagingSenderId: "847981940747",
  appId: "1:847981940747:android:eb82f08a16c340f5f0b70e"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

const MEASUREMENTS_COLLECTION = 'measurements';

// Helper function to parse date and create timestamp for sorting
const createTimestamp = (dateStr) => {
  if (!dateStr) return Date.now();
  let parsedDate;
  if (dateStr.includes('-')) {
    const parts = dateStr.split('-');
    if (parts[0].length === 4) {
      parsedDate = new Date(parseInt(parts[0]), parseInt(parts[1]) - 1, parseInt(parts[2]));
    } else {
      parsedDate = new Date(parseInt(parts[2]), parseInt(parts[1]) - 1, parseInt(parts[0]));
    }
  } else {
    parsedDate = new Date(dateStr);
  }
  return parsedDate.getTime();
};

// Test search functionality
async function testSearchAll() {
  try {
    console.log('🧪 Testing search across all measurements...');
    
    const measurementsRef = collection(db, MEASUREMENTS_COLLECTION);
    const querySnapshot = await getDocs(measurementsRef);
    
    console.log(`📊 Total measurements in database: ${querySnapshot.size}`);
    
    // Get some sample data to test search with
    const sampleMeasurements = [];
    let count = 0;
    querySnapshot.forEach((doc) => {
      if (count < 10) { // Get first 10 for testing
        const data = doc.data();
        sampleMeasurements.push({
          id: doc.id,
          name: data.name || 'Unknown',
          phoneNo: data.phoneNo || '',
          billNo: data.billNo || '',
          date: data.date || '',
          timestamp: data.timestamp || createTimestamp(data.date)
        });
        count++;
      }
    });
    
    console.log('\n📝 Sample measurements for testing:');
    sampleMeasurements.forEach((measurement, index) => {
      console.log(`${index + 1}. Name: "${measurement.name}", Phone: "${measurement.phoneNo}", Bill: "${measurement.billNo}"`);
    });
    
    // Test search function
    const searchAllMeasurements = async (searchTerm) => {
      if (!searchTerm || searchTerm.trim() === '') {
        return [];
      }

      const normalizedSearchTerm = searchTerm.toLowerCase().trim();
      const matchingMeasurements = [];

      querySnapshot.forEach((doc) => {
        try {
          const data = doc.data();
          
          const name = (data.name || '').toLowerCase();
          const phoneNo = data.phoneNo || '';
          const billNo = data.billNo || '';

          if (name.includes(normalizedSearchTerm) ||
              phoneNo.includes(normalizedSearchTerm) ||
              billNo.includes(normalizedSearchTerm)) {
            
            const measurement = {
              id: doc.id,
              name: data.name || 'Unknown Customer',
              phoneNo: data.phoneNo || '',
              billNo: data.billNo || '',
              date: data.date || new Date().toISOString().split('T')[0],
              timestamp: data.timestamp || createTimestamp(data.date)
            };

            matchingMeasurements.push(measurement);
          }
        } catch (docError) {
          console.error('❌ Error processing document during search:', docError);
        }
      });

      // Sort by timestamp (newest first)
      matchingMeasurements.sort((a, b) => {
        const timestampA = a.timestamp || 0;
        const timestampB = b.timestamp || 0;
        return timestampB - timestampA;
      });

      return matchingMeasurements;
    };
    
    // Test different search terms
    const testSearches = [
      'Unknown', // Should find many results
      '9', // Search by phone number digit
      '1', // Search by bill number
    ];
    
    for (const searchTerm of testSearches) {
      console.log(`\n🔍 Testing search for: "${searchTerm}"`);
      const results = await searchAllMeasurements(searchTerm);
      console.log(`✅ Found ${results.length} results`);
      
      if (results.length > 0) {
        console.log('📋 First 5 results:');
        results.slice(0, 5).forEach((result, index) => {
          console.log(`  ${index + 1}. ${result.name} - ${result.phoneNo} - Bill: ${result.billNo}`);
        });
      }
    }
    
    console.log('\n🎉 Search functionality test completed successfully!');
    
  } catch (error) {
    console.error('❌ Search test failed:', error);
  }
}

// Run the test
testSearchAll().then(() => {
  console.log('🏁 Search test completed');
  process.exit(0);
}).catch((error) => {
  console.error('💥 Search test failed:', error);
  process.exit(1);
});
