// Script to add deliveryTimestamp to existing measurements
import { initializeApp } from 'firebase/app';
import { getFirestore, collection, getDocs, updateDoc, doc, query, where } from 'firebase/firestore';

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyCJ11B1BATse018MOQ1NTKQY45zY6fg-SU",
  authDomain: "tailor-dd259.firebaseapp.com",
  projectId: "tailor-dd259",
  storageBucket: "tailor-dd259.appspot.com",
  messagingSenderId: "847981940747",
  appId: "1:847981940747:android:eb82f08a16c340f5f0b70e"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

const MEASUREMENTS_COLLECTION = 'measurements';

// Helper function to parse date and create timestamp for sorting
const createTimestamp = (dateStr) => {
  if (!dateStr) return Date.now(); // Use current timestamp if no date

  // Handle both DD-MM-YYYY and YYYY-MM-DD formats
  let parsedDate;
  if (dateStr.includes('-')) {
    const parts = dateStr.split('-');
    if (parts[0].length === 4) {
      // YYYY-MM-DD format
      parsedDate = new Date(parseInt(parts[0]), parseInt(parts[1]) - 1, parseInt(parts[2]));
    } else {
      // DD-MM-YYYY format
      parsedDate = new Date(parseInt(parts[2]), parseInt(parts[1]) - 1, parseInt(parts[0]));
    }
  } else {
    parsedDate = new Date(dateStr);
  }

  return parsedDate.getTime();
};

// Fix delivery timestamps for 2025 measurements only
async function fixDeliveryTimestamps() {
  try {
    console.log('🔍 Checking 2025 measurements for missing delivery timestamps...');
    
    // Create timestamp for start of 2025 (January 1, 2025)
    const start2025 = new Date('2025-01-01').getTime();
    // Create timestamp for end of 2025 (December 31, 2025)
    const end2025 = new Date('2025-12-31T23:59:59').getTime();
    
    const measurementsRef = collection(db, MEASUREMENTS_COLLECTION);
    
    // Only get 2025 measurements
    const querySnapshot = await getDocs(query(
      measurementsRef,
      where('timestamp', '>=', start2025),
      where('timestamp', '<=', end2025)
    ));
    
    console.log(`📊 Found ${querySnapshot.size} measurements from 2025`);
    
    let updatedCount = 0;
    
    for (const docSnapshot of querySnapshot.docs) {
      const data = docSnapshot.data();
      
      // Check if deliveryTimestamp is missing or invalid
      if (!data.deliveryTimestamp || typeof data.deliveryTimestamp !== 'number') {
        try {
          // Use deliveryDate if available, otherwise fall back to date
          const dateToUse = data.deliveryDate || data.date;
          const deliveryTimestamp = createTimestamp(dateToUse);
          
          // Update the document with the delivery timestamp
          await updateDoc(doc(db, MEASUREMENTS_COLLECTION, docSnapshot.id), {
            deliveryTimestamp: deliveryTimestamp
          });
          
          updatedCount++;
          console.log(`✅ Updated ${docSnapshot.id} with deliveryTimestamp: ${deliveryTimestamp} (${new Date(deliveryTimestamp).toLocaleDateString()}) from ${dateToUse}`);
          
        } catch (error) {
          console.error(`❌ Error updating ${docSnapshot.id}:`, error);
        }
      }
    }
    
    console.log(`🎉 Delivery timestamp fix completed! Updated ${updatedCount} measurements from 2025.`);
    
    // Test the query to make sure it works now
    console.log('🧪 Testing orderBy deliveryTimestamp query...');
    const testQuery = query(
      measurementsRef,
      where('timestamp', '>=', start2025),
      where('timestamp', '<=', end2025)
    );
    const testSnapshot = await getDocs(testQuery);
    console.log(`✅ Query test successful! Found ${testSnapshot.size} 2025 measurements.`);
    
  } catch (error) {
    console.error('❌ Error fixing delivery timestamps:', error);
  }
}

// Run the fix
fixDeliveryTimestamps().then(() => {
  console.log('🏁 Delivery timestamp fix script completed');
  process.exit(0);
}).catch((error) => {
  console.error('💥 Delivery timestamp fix script failed:', error);
  process.exit(1);
});
