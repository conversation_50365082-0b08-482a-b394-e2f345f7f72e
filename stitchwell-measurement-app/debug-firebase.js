// Debug script to test Firebase connection and add sample data
import { initializeApp } from 'firebase/app';
import { getFirestore, collection, addDoc, getDocs, query } from 'firebase/firestore';

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyCJ11B1BATse018MOQ1NTKQY45zY6fg-SU",
  authDomain: "tailor-dd259.firebaseapp.com",
  projectId: "tailor-dd259",
  storageBucket: "tailor-dd259.appspot.com",
  messagingSenderId: "847981940747",
  appId: "1:847981940747:android:eb82f08a16c340f5f0b70e"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

const MEASUREMENTS_COLLECTION = 'measurements';

// Test Firebase connection
async function testConnection() {
  try {
    console.log('🔍 Testing Firebase connection...');
    const measurementsRef = collection(db, MEASUREMENTS_COLLECTION);
    const q = query(measurementsRef);
    
    const querySnapshot = await getDocs(q);
    console.log('✅ Firebase connection successful!');
    console.log('📊 Documents found:', querySnapshot.size);
    
    if (querySnapshot.size === 0) {
      console.log('📭 No measurements found. Adding sample data...');
      await addSampleData();
    } else {
      console.log('📋 Existing measurements:');
      querySnapshot.forEach((doc) => {
        const data = doc.data();
        console.log(`- ${data.name} (${data.phoneNo}) - Bill: ${data.billNo}`);
      });
    }
    
    return true;
  } catch (error) {
    console.error('❌ Firebase connection failed:', error);
    return false;
  }
}

// Add sample data
async function addSampleData() {
  try {
    const sampleMeasurements = [
      {
        name: 'John Doe',
        phoneNo: '9876543210',
        billNo: 'B001',
        date: '2024-12-20',
        deliveryDate: '2024-12-25',
        imageUrl: '',
        measurements: {
          chest: '40',
          waist: '32',
          length: '28'
        },
        notes: 'Regular fit shirt',
        timestamp: Date.now()
      },
      {
        name: 'Jane Smith',
        phoneNo: '9876543211',
        billNo: 'B002',
        date: '2024-12-19',
        deliveryDate: '2024-12-24',
        imageUrl: '',
        measurements: {
          chest: '36',
          waist: '28',
          length: '26'
        },
        notes: 'Formal dress',
        timestamp: Date.now() - 86400000 // 1 day ago
      },
      {
        name: 'Mike Johnson',
        phoneNo: '9876543212',
        billNo: 'B003',
        date: '2024-12-18',
        deliveryDate: '2024-12-23',
        imageUrl: '',
        measurements: {
          chest: '42',
          waist: '34',
          length: '30'
        },
        notes: 'Casual wear',
        timestamp: Date.now() - 172800000 // 2 days ago
      }
    ];

    const measurementsRef = collection(db, MEASUREMENTS_COLLECTION);
    
    for (const measurement of sampleMeasurements) {
      await addDoc(measurementsRef, measurement);
      console.log(`✅ Added sample measurement for ${measurement.name}`);
    }
    
    console.log('🎉 Sample data added successfully!');
  } catch (error) {
    console.error('❌ Error adding sample data:', error);
  }
}

// Run the test
testConnection().then(() => {
  console.log('🏁 Debug script completed');
}).catch((error) => {
  console.error('💥 Debug script failed:', error);
});
