// Quick test script to verify delivery date sorting
import { initializeApp } from 'firebase/app';
import { getFirestore, collection, query, orderBy, limit, getDocs, where } from 'firebase/firestore';

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyCJ11B1BATse018MOQ1NTKQY45zY6fg-SU",
  authDomain: "tailor-dd259.firebaseapp.com",
  projectId: "tailor-dd259",
  storageBucket: "tailor-dd259.appspot.com",
  messagingSenderId: "847981940747",
  appId: "1:847981940747:android:eb82f08a16c340f5f0b70e"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

const MEASUREMENTS_COLLECTION = 'measurements';

// Test delivery date sorting
async function testDeliverySort() {
  try {
    console.log('🧪 Testing delivery date sorting for 2025 measurements...');
    
    const measurementsRef = collection(db, MEASUREMENTS_COLLECTION);
    
    // Create timestamp for start of 2025 (January 1, 2025)
    const start2025 = new Date('2025-01-01').getTime();
    // Create timestamp for end of 2025 (December 31, 2025)
    const end2025 = new Date('2025-12-31T23:59:59').getTime();
    
    // Test query with delivery date sorting
    const testQuery = query(
      measurementsRef,
      orderBy('deliveryTimestamp', 'desc'),
      where('timestamp', '>=', start2025),
      where('timestamp', '<=', end2025),
      limit(10) // Get top 10 to see the sorting
    );

    const querySnapshot = await getDocs(testQuery);
    
    console.log(`✅ Delivery date sorting test successful!`);
    console.log(`📊 Retrieved ${querySnapshot.size} measurements sorted by delivery date`);
    
    if (!querySnapshot.empty) {
      console.log('\n📝 Top 10 measurements by latest delivery date:');
      querySnapshot.docs.forEach((doc, index) => {
        const data = doc.data();
        const deliveryDate = new Date(data.deliveryTimestamp).toLocaleDateString();
        console.log(`${index + 1}. ${data.name || 'Unknown'} - Delivery: ${data.deliveryDate || 'N/A'} (${deliveryDate})`);
      });
    } else {
      console.log('📭 No measurements found for 2025');
    }
    
  } catch (error) {
    console.error('❌ Delivery date sorting test failed:', error);
  }
}

// Run the test
testDeliverySort().then(() => {
  console.log('🏁 Delivery date sorting test completed');
  process.exit(0);
}).catch((error) => {
  console.error('💥 Delivery date sorting test failed:', error);
  process.exit(1);
});
