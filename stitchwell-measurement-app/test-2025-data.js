// Quick test script to check 2025 measurements
import { initializeApp } from 'firebase/app';
import { getFirestore, collection, query, orderBy, limit, getDocs, where } from 'firebase/firestore';

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyCJ11B1BATse018MOQ1NTKQY45zY6fg-SU",
  authDomain: "tailor-dd259.firebaseapp.com",
  projectId: "tailor-dd259",
  storageBucket: "tailor-dd259.appspot.com",
  messagingSenderId: "847981940747",
  appId: "1:847981940747:android:eb82f08a16c340f5f0b70e"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

const MEASUREMENTS_COLLECTION = 'measurements';

// Test 2025 data query
async function test2025Data() {
  try {
    console.log('🧪 Testing 2025 measurements query...');
    
    const measurementsRef = collection(db, MEASUREMENTS_COLLECTION);
    
    // Create timestamp for start of 2025 (January 1, 2025)
    const start2025 = new Date('2025-01-01').getTime();
    // Create timestamp for end of 2025 (December 31, 2025)
    const end2025 = new Date('2025-12-31T23:59:59').getTime();
    
    console.log('📅 2025 timestamp range:', {
      start: start2025,
      end: end2025,
      startDate: new Date(start2025).toLocaleDateString(),
      endDate: new Date(end2025).toLocaleDateString()
    });
    
    // Test query with 2025 filter
    const testQuery = query(
      measurementsRef,
      orderBy('timestamp', 'desc'),
      where('timestamp', '>=', start2025),
      where('timestamp', '<=', end2025),
      limit(50) // Get up to 50 to see how many we have
    );

    const querySnapshot = await getDocs(testQuery);
    
    console.log(`✅ 2025 query successful!`);
    console.log(`📊 Retrieved ${querySnapshot.size} measurements from 2025`);
    
    if (!querySnapshot.empty) {
      console.log('\n📝 Sample 2025 measurements:');
      querySnapshot.docs.slice(0, 5).forEach((doc, index) => {
        const data = doc.data();
        console.log(`${index + 1}. ${data.name || 'Unknown'} - ${data.date} (timestamp: ${new Date(data.timestamp).toLocaleDateString()})`);
      });
    } else {
      console.log('📭 No measurements found for 2025');
    }
    
  } catch (error) {
    console.error('❌ 2025 query test failed:', error);
  }
}

// Run the test
test2025Data().then(() => {
  console.log('🏁 2025 data test completed');
  process.exit(0);
}).catch((error) => {
  console.error('💥 2025 data test failed:', error);
  process.exit(1);
});
