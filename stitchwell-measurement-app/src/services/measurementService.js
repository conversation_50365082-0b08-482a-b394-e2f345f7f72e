import {
  collection,
  addDoc,
  updateDoc,
  deleteDoc,
  doc,
  getDocs,
  getDoc,
  query,
  orderBy,
  onSnapshot,
  limit,
  startAfter
} from 'firebase/firestore';
import { db } from '../../firebaseConfig';

const MEASUREMENTS_COLLECTION = 'measurements';

/**
 * Test Firebase connection
 * @returns {Promise<boolean>} True if connection is successful
 */
export const testFirebaseConnection = async () => {
  try {
    console.log('🔍 Testing Firebase connection...');
    const measurementsRef = collection(db, MEASUREMENTS_COLLECTION);
    const q = query(measurementsRef);

    // Try to get a small sample to test connection
    const querySnapshot = await getDocs(q);
    console.log('✅ Firebase connection successful, documents found:', querySnapshot.size);
    return true;
  } catch (error) {
    console.error('❌ Firebase connection failed:', error);
    return false;
  }
};

/**
 * Get all measurements from Firestore
 * @returns {Promise<Array>} Array of measurement objects
 */
export const getAllMeasurements = async () => {
  try {
    const measurementsRef = collection(db, MEASUREMENTS_COLLECTION);
    // Try to get measurements ordered by date, but handle if date field doesn't exist
    let q;
    try {
      q = query(measurementsRef, orderBy('date', 'desc'));
    } catch (err) {
      console.warn('Could not order by date, using default order:', err);
      q = query(measurementsRef);
    }

    const querySnapshot = await getDocs(q);

    const measurements = [];
    querySnapshot.forEach((doc) => {
      try {
        const data = doc.data();
        // Ensure required fields exist and map to correct field names
        const measurement = {
          id: doc.id,
          name: data.name || 'Unknown Customer',
          phoneNo: data.phoneNo || '',
          billNo: data.billNo || '',
          date: data.date || new Date().toISOString().split('T')[0],
          deliveryDate: data.deliveryDate || '',
          imageUrl: data.imageUrl || '',
          measurements: data.measurements || {},
          notes: data.notes || ''
        };

        measurements.push(measurement);
      } catch (docError) {
        console.error('Error processing document:', docError);
        // Continue with next document instead of failing the whole operation
      }
    });

    return measurements;
  } catch (error) {
    console.error('Error getting measurements:', error);
    throw error;
  }
};

/**
 * Get a single measurement by ID
 * @param {string} id - Measurement document ID
 * @returns {Promise<Object>} Measurement object
 */
export const getMeasurementById = async (id) => {
  try {
    const docRef = doc(db, MEASUREMENTS_COLLECTION, id);
    const docSnap = await getDoc(docRef);

    if (docSnap.exists()) {
      const data = docSnap.data();

      // Ensure required fields exist and map to correct field names
      return {
        id: docSnap.id,
        name: data.name || 'Unknown Customer',
        phoneNo: data.phoneNo || '',
        billNo: data.billNo || '',
        date: data.date || new Date().toISOString().split('T')[0],
        deliveryDate: data.deliveryDate || '',
        imageUrl: data.imageUrl || '',
        measurements: data.measurements || {},
        notes: data.notes || ''
      };
    } else {
      throw new Error('Measurement not found');
    }
  } catch (error) {
    console.error('Error getting measurement:', error);
    throw error;
  }
};

/**
 * Search measurements across entire database by name, phone, or bill number
 * @param {string} searchTerm - Search term to match against customer name, phone, or bill number
 * @returns {Promise<Array>} Array of matching measurement objects
 */
export const searchAllMeasurements = async (searchTerm) => {
  try {
    console.log('🔍 Searching all measurements for:', searchTerm);

    if (!searchTerm || searchTerm.trim() === '') {
      return [];
    }

    const measurementsRef = collection(db, MEASUREMENTS_COLLECTION);

    // Get all measurements for search (we need to search across entire database)
    const querySnapshot = await getDocs(measurementsRef);

    console.log(`📊 Searching through ${querySnapshot.size} total measurements`);

    const normalizedSearchTerm = searchTerm.toLowerCase().trim();
    const matchingMeasurements = [];

    // Helper function to parse date and create timestamp for sorting
    const createTimestamp = (dateStr) => {
      if (!dateStr) return Date.now();
      let parsedDate;
      if (dateStr.includes('-')) {
        const parts = dateStr.split('-');
        if (parts[0].length === 4) {
          parsedDate = new Date(parseInt(parts[0]), parseInt(parts[1]) - 1, parseInt(parts[2]));
        } else {
          parsedDate = new Date(parseInt(parts[2]), parseInt(parts[1]) - 1, parseInt(parts[0]));
        }
      } else {
        parsedDate = new Date(dateStr);
      }
      return parsedDate.getTime();
    };

    querySnapshot.forEach((doc) => {
      try {
        const data = doc.data();

        // Check if this measurement matches the search term
        const name = (data.name || '').toLowerCase();
        const phoneNo = data.phoneNo || '';
        const billNo = data.billNo || '';

        if (name.includes(normalizedSearchTerm) ||
            phoneNo.includes(normalizedSearchTerm) ||
            billNo.includes(normalizedSearchTerm)) {

          // Create measurement object with all required fields
          const measurement = {
            id: doc.id,
            name: data.name || 'Unknown Customer',
            phoneNo: data.phoneNo || '',
            billNo: data.billNo || '',
            date: data.date || new Date().toISOString().split('T')[0],
            deliveryDate: data.deliveryDate || '',
            imageUrl: data.imageUrl || '',
            measurements: data.measurements || {},
            notes: data.notes || '',
            timestamp: data.timestamp || createTimestamp(data.date),
            deliveryTimestamp: data.deliveryTimestamp || createTimestamp(data.deliveryDate || data.date)
          };

          matchingMeasurements.push(measurement);
        }
      } catch (docError) {
        console.error('❌ Error processing document during search:', docError);
      }
    });

    // Sort search results by timestamp (newest first)
    matchingMeasurements.sort((a, b) => {
      const timestampA = a.timestamp || 0;
      const timestampB = b.timestamp || 0;
      return timestampB - timestampA;
    });

    console.log(`✅ Search completed. Found ${matchingMeasurements.length} matching measurements`);
    return matchingMeasurements;

  } catch (error) {
    console.error('❌ Error searching all measurements:', error);
    throw error;
  }
};

/**
 * Search measurements by customer name (legacy function - kept for compatibility)
 * @param {string} searchTerm - Search term to match against customer name
 * @returns {Promise<Array>} Array of matching measurement objects
 */
export const searchMeasurementsByName = async (searchTerm) => {
  return searchAllMeasurements(searchTerm);
};

/**
 * Add a new measurement to Firestore
 * @param {Object} measurementData - Measurement data object
 * @returns {Promise<string>} ID of the newly created measurement
 */
export const addMeasurement = async (measurementData) => {
  try {
    const measurementsRef = collection(db, MEASUREMENTS_COLLECTION);
    const docRef = await addDoc(measurementsRef, {
      ...measurementData,
      date: measurementData.date || new Date().toISOString().split('T')[0],
      timestamp: Date.now() // Add timestamp for proper sorting
    });

    return docRef.id;
  } catch (error) {
    console.error('Error adding measurement:', error);
    throw error;
  }
};

/**
 * Update an existing measurement
 * @param {string} id - Measurement document ID
 * @param {Object} measurementData - Updated measurement data
 * @returns {Promise<void>}
 */
export const updateMeasurement = async (id, measurementData) => {
  try {
    const docRef = doc(db, MEASUREMENTS_COLLECTION, id);
    await updateDoc(docRef, measurementData);
  } catch (error) {
    console.error('Error updating measurement:', error);
    throw error;
  }
};

/**
 * Delete a measurement
 * @param {string} id - Measurement document ID
 * @returns {Promise<void>}
 */
export const deleteMeasurement = async (id) => {
  try {
    const docRef = doc(db, MEASUREMENTS_COLLECTION, id);
    await deleteDoc(docRef);
  } catch (error) {
    console.error('Error deleting measurement:', error);
    throw error;
  }
};

/**
 * Subscribe to real-time updates of measurements with pagination
 * @param {Function} callback - Function to call with updated measurements and pagination info
 * @param {number} pageSize - Number of items to load per page
 * @param {Function} onError - Function to call on error
 * @returns {{ unsubscribe: Function, loadMore: Function }} Object containing unsubscribe function and loadMore function
 */
export const subscribeMeasurements = (callback, pageSize = 15, onError = () => {}) => {
  console.log('🔄 Setting up measurements subscription with pageSize:', pageSize);

  try {
    const measurementsRef = collection(db, MEASUREMENTS_COLLECTION);

    // Keep track of pagination state
    let isLoading = false;
    let allLoaded = false;
    let loadedMeasurements = []; // Store loaded measurements
    let lastDoc = null; // Keep track of last document for pagination
    let unsubscribeFunction = null;

    // Add timeout to prevent infinite loading
    const timeoutId = setTimeout(() => {
      console.error('⏰ Subscription timeout - Firebase connection might be slow');
      onError(new Error('Connection timeout. Please check your internet connection and try again.'));
    }, 15000); // 15 second timeout

    // Helper function to parse date and create timestamp for sorting
    const createTimestamp = (dateStr) => {
      if (!dateStr) return Date.now(); // Use current timestamp if no date

      // Handle both DD-MM-YYYY and YYYY-MM-DD formats
      let parsedDate;
      if (dateStr.includes('-')) {
        const parts = dateStr.split('-');
        if (parts[0].length === 4) {
          // YYYY-MM-DD format
          parsedDate = new Date(parseInt(parts[0]), parseInt(parts[1]) - 1, parseInt(parts[2]));
        } else {
          // DD-MM-YYYY format
          parsedDate = new Date(parseInt(parts[2]), parseInt(parts[1]) - 1, parseInt(parts[0]));
        }
      } else {
        parsedDate = new Date(dateStr);
      }

      return parsedDate.getTime();
    };

    // Function to load initial page
    const loadInitialPage = () => {
      console.log('📊 Loading initial page of measurements (2025 data, sorted by delivery date)...');

      // Create timestamp for start of 2025 (January 1, 2025)
      const start2025 = new Date('2025-01-01').getTime();
      // Create timestamp for end of 2025 (December 31, 2025)
      const end2025 = new Date('2025-12-31T23:59:59').getTime();

      // Create query with limit for first page - order by deliveryTimestamp descending
      // Note: We'll filter for 2025 data client-side to avoid complex index requirements
      const initialQuery = query(
        measurementsRef,
        orderBy('deliveryTimestamp', 'desc'),
        limit(pageSize * 3) // Get more records to account for filtering
      );

      unsubscribeFunction = onSnapshot(initialQuery, (querySnapshot) => {
        try {
          // Clear timeout since we got a response
          clearTimeout(timeoutId);
          console.log('📊 Received initial measurements snapshot, count:', querySnapshot.size);

          if (querySnapshot.empty) {
            console.log('📭 No measurements found in database');
            allLoaded = true;
            loadedMeasurements = [];
            callback([], { allLoaded: true, isLoading: false });
            return;
          }

          const measurements = [];
          querySnapshot.forEach((doc) => {
            try {
              const data = doc.data();

              // Check if this measurement is from 2025
              const measurementTimestamp = data.timestamp || createTimestamp(data.date);
              if (measurementTimestamp >= start2025 && measurementTimestamp <= end2025) {
                // Ensure required fields exist and map to correct field names
                const measurement = {
                  id: doc.id,
                  name: data.name || 'Unknown Customer',
                  phoneNo: data.phoneNo || '',
                  billNo: data.billNo || '',
                  date: data.date || new Date().toISOString().split('T')[0],
                  deliveryDate: data.deliveryDate || '',
                  imageUrl: data.imageUrl || '',
                  measurements: data.measurements || {},
                  notes: data.notes || '',
                  timestamp: measurementTimestamp, // Add timestamp for sorting
                  deliveryTimestamp: data.deliveryTimestamp || createTimestamp(data.deliveryDate || data.date) // Add delivery timestamp for sorting
                };

                measurements.push(measurement);
              }
            } catch (docError) {
              console.error('❌ Error processing document:', docError);
              // Continue with next document instead of failing the whole operation
            }
          });

          // Limit to pageSize after filtering
          const limitedMeasurements = measurements.slice(0, pageSize);

          // Store loaded measurements and last document for pagination
          loadedMeasurements = limitedMeasurements;
          lastDoc = querySnapshot.docs[querySnapshot.docs.length - 1];
          allLoaded = measurements.length < pageSize; // Check if we have fewer than pageSize after filtering

          console.log('✅ Successfully loaded initial measurements:', {
            totalFiltered: measurements.length,
            count: loadedMeasurements.length,
            allLoaded
          });

          // Call the callback with the measurements
          callback(loadedMeasurements, {
            allLoaded,
            isLoading: false
          });
        } catch (error) {
          console.error('❌ Error processing snapshot:', error);
          clearTimeout(timeoutId);
          onError(error);
        }
      }, (error) => {
        console.error('❌ Error listening to measurements:', error);
        clearTimeout(timeoutId);
        onError(error);
      });
    };

    // Load initial page
    loadInitialPage();

    // Function to load more items (server-side pagination)
    const loadMore = async () => {
      // If already loading or all loaded, don't proceed
      if (isLoading || allLoaded || !lastDoc) {
        console.log('🚫 Cannot load more:', { isLoading, allLoaded, hasLastDoc: !!lastDoc });
        return;
      }

      console.log('📊 Loading more measurements...');
      isLoading = true;

      // Notify that we're loading
      callback(loadedMeasurements, { isLoading: true, allLoaded });

      try {
        // Create timestamp for start of 2025 (January 1, 2025)
        const start2025 = new Date('2025-01-01').getTime();
        // Create timestamp for end of 2025 (December 31, 2025)
        const end2025 = new Date('2025-12-31T23:59:59').getTime();

        // Create query for next page using startAfter, sort by delivery date
        // Note: We'll filter for 2025 data client-side to avoid complex index requirements
        const nextQuery = query(
          measurementsRef,
          orderBy('deliveryTimestamp', 'desc'),
          startAfter(lastDoc),
          limit(pageSize * 3) // Get more records to account for filtering
        );

        const querySnapshot = await getDocs(nextQuery);

        if (querySnapshot.empty) {
          console.log('📭 No more measurements to load');
          allLoaded = true;
          callback(loadedMeasurements, { isLoading: false, allLoaded: true });
          return;
        }

        const newMeasurements = [];
        querySnapshot.forEach((doc) => {
          try {
            const data = doc.data();

            // Check if this measurement is from 2025
            const measurementTimestamp = data.timestamp || createTimestamp(data.date);
            if (measurementTimestamp >= start2025 && measurementTimestamp <= end2025) {
              // Ensure required fields exist and map to correct field names
              const measurement = {
                id: doc.id,
                name: data.name || 'Unknown Customer',
                phoneNo: data.phoneNo || '',
                billNo: data.billNo || '',
                date: data.date || new Date().toISOString().split('T')[0],
                deliveryDate: data.deliveryDate || '',
                imageUrl: data.imageUrl || '',
                measurements: data.measurements || {},
                notes: data.notes || '',
                timestamp: measurementTimestamp,
                deliveryTimestamp: data.deliveryTimestamp || createTimestamp(data.deliveryDate || data.date) // Add delivery timestamp for sorting
              };

              newMeasurements.push(measurement);
            }
          } catch (docError) {
            console.error('❌ Error processing document:', docError);
          }
        });

        // Limit to pageSize after filtering
        const limitedNewMeasurements = newMeasurements.slice(0, pageSize);

        // Append new measurements to existing ones
        loadedMeasurements = [...loadedMeasurements, ...limitedNewMeasurements];
        lastDoc = querySnapshot.docs[querySnapshot.docs.length - 1];
        allLoaded = newMeasurements.length < pageSize; // Check if we have fewer than pageSize after filtering

        console.log('✅ Successfully loaded more measurements:', {
          totalFiltered: newMeasurements.length,
          newCount: limitedNewMeasurements.length,
          totalCount: loadedMeasurements.length,
          allLoaded
        });

        // Call the callback with updated measurements
        callback(loadedMeasurements, {
          isLoading: false,
          allLoaded
        });
      } catch (error) {
        console.error('❌ Error loading more measurements:', error);
        onError(error);
        callback(loadedMeasurements, { isLoading: false, allLoaded });
      } finally {
        isLoading = false;
      }
    };

    // Return both the unsubscribe function and the loadMore function
    return {
      unsubscribe: () => {
        clearTimeout(timeoutId);
        if (unsubscribeFunction) {
          unsubscribeFunction();
        }
      },
      loadMore
    };
  } catch (error) {
    console.error('❌ Error setting up measurements listener:', error);
    clearTimeout(timeoutId);
    onError(error);
    // Return no-op functions in case of error
    return {
      unsubscribe: () => {},
      loadMore: () => {}
    };
  }
};
