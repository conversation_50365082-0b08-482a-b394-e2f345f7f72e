import React, { useState, useEffect } from 'react';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { RootStackParamList } from '../navigation/types';
import {
  Button,
  H2,
  XStack,
  YStack,
  Input,
  Text,
  TextArea,
  Card,
} from 'tamagui';
import {
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  View,
  Alert,
  TouchableOpacity,
  Image,
  ActionSheetIOS,
  ActivityIndicator
} from 'react-native';
import * as ImagePicker from 'expo-image-picker';
import { Measurement } from '../types';
import { colors, borderRadius, spacing } from '../theme/colors';
import { getMeasurementById, updateMeasurement } from '../services/measurementService';
import { uploadImage } from '../services/storageService';
import {
  UserIcon,
  PhoneIcon,
  BillIcon,
  CalendarIcon,
  MoneyIcon,
  FileTextIcon,
  SaveIcon,
  CameraIcon,
  BackIcon,
  ZoomIcon
} from '../components/TabIcons';
import ImageZoomModal from '../components/ImageZoomModal';
import DatePickerInput from '../components/DatePickerInput';

type Props = NativeStackScreenProps<RootStackParamList, 'EditBill'>;

const EditBillScreen = ({ navigation, route }: Props) => {
  const { billId } = route.params;
  const [customerName, setCustomerName] = useState('');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [billNumber, setBillNumber] = useState('');
  const [amount, setAmount] = useState('');
  const [deliveryDate, setDeliveryDate] = useState('');
  const [notes, setNotes] = useState('');
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const [imageUri, setImageUri] = useState<string | null>(null);
  const [uploadingImage, setUploadingImage] = useState(false);
  const [originalBill, setOriginalBill] = useState<Measurement | null>(null);
  const [imageZoomVisible, setImageZoomVisible] = useState(false);

  useEffect(() => {
    loadBillData();
  }, [billId]);

  const loadBillData = async () => {
    try {
      setInitialLoading(true);
      const billData = await getMeasurementById(billId);
      setOriginalBill(billData);

      // Populate form fields
      setCustomerName(billData.name || '');
      setPhoneNumber(billData.phoneNo || '');
      setBillNumber(billData.billNo || '');
      setAmount(''); // Amount is not stored in current Measurement type
      setDeliveryDate(billData.deliveryDate || '');
      setNotes(billData.notes || '');
      setImageUri(billData.imageUrl || null);
    } catch (error) {
      console.error('Error loading bill data:', error);
      Alert.alert('Error', 'Failed to load bill data');
      navigation.goBack();
    } finally {
      setInitialLoading(false);
    }
  };

  // Request camera permissions
  React.useEffect(() => {
    (async () => {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission needed', 'Sorry, we need camera roll permissions to upload images.');
      }
    })();
  }, []);

  const pickImage = async () => {
    const showImagePicker = () => {
      if (Platform.OS === 'ios') {
        ActionSheetIOS.showActionSheetWithOptions(
          {
            options: ['Cancel', 'Take Photo', 'Choose from Gallery'],
            cancelButtonIndex: 0,
          },
          (buttonIndex) => {
            if (buttonIndex === 1) {
              openCamera();
            } else if (buttonIndex === 2) {
              openGallery();
            }
          }
        );
      } else {
        Alert.alert(
          'Select Image',
          'Choose an option',
          [
            { text: 'Cancel', style: 'cancel' },
            { text: 'Take Photo', onPress: openCamera },
            { text: 'Choose from Gallery', onPress: openGallery },
          ]
        );
      }
    };

    const openCamera = async () => {
      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission needed', 'Camera permission is required to take photos.');
        return;
      }

      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        setImageUri(result.assets[0].uri);
      }
    };

    const openGallery = async () => {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        setImageUri(result.assets[0].uri);
      }
    };

    showImagePicker();
  };

  const validateForm = () => {
    if (!customerName.trim()) {
      Alert.alert('Validation Error', 'Customer name is required');
      return false;
    }
    if (!phoneNumber.trim()) {
      Alert.alert('Validation Error', 'Phone number is required');
      return false;
    }
    if (!billNumber.trim()) {
      Alert.alert('Validation Error', 'Bill number is required');
      return false;
    }
    if (!deliveryDate.trim()) {
      Alert.alert('Validation Error', 'Delivery date is required');
      return false;
    }
    return true;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    setLoading(true);
    try {
      let uploadedImageUrl = imageUri;
      
      // Upload new image if it's a local URI (not a URL)
      if (imageUri && !imageUri.startsWith('http')) {
        setUploadingImage(true);
        try {
          uploadedImageUrl = await uploadImage(imageUri);
        } catch (imageError) {
          console.error('Error uploading image:', imageError);
          Alert.alert('Warning', 'Failed to upload image, but bill will be updated without it.');
        } finally {
          setUploadingImage(false);
        }
      }

      // Update the measurement record
      const updatedBill: Partial<Measurement> = {
        name: customerName.trim(),
        phoneNo: phoneNumber.trim(),
        billNo: billNumber.trim(),
        deliveryDate: deliveryDate.trim(),
        imageUrl: uploadedImageUrl || '',
        notes: notes.trim(),
      };

      await updateMeasurement(billId, updatedBill);
      
      Alert.alert(
        'Success', 
        'Bill updated successfully!',
        [
          {
            text: 'OK',
            onPress: () => navigation.goBack()
          }
        ]
      );
    } catch (error) {
      console.error('Error updating bill:', error);
      Alert.alert('Error', 'Failed to update bill. Please try again.');
    } finally {
      setLoading(false);
      setUploadingImage(false);
    }
  };

  if (initialLoading) {
    return (
      <YStack f={1} backgroundColor={colors.background} justifyContent="center" alignItems="center">
        <ActivityIndicator size="large" color={colors.primary} />
        <Text marginTop="$4" color={colors.textSecondary}>Loading bill data...</Text>
      </YStack>
    );
  }

  return (
    <YStack f={1} backgroundColor={colors.background}>
      {/* Clean Header */}
      <YStack 
        backgroundColor={colors.card}
        paddingHorizontal={spacing.lg}
        paddingTop={spacing.xl}
        paddingBottom={spacing.md}
        borderBottomWidth={1}
        borderBottomColor={colors.border}
      >
        <XStack alignItems="center" justifyContent="space-between" marginBottom="$3">
          <XStack alignItems="center" space="$3">
            <TouchableOpacity
              onPress={() => navigation.goBack()}
              style={{
                width: 32,
                height: 32,
                borderRadius: 16,
                backgroundColor: colors.backgroundAlt,
                justifyContent: 'center',
                alignItems: 'center',
              }}
            >
              <BackIcon size={18} color={colors.text} />
            </TouchableOpacity>
            <YStack>
              <H2
                color={colors.text}
                fontSize={24}
                fontWeight="700"
                fontFamily="$heading"
                marginBottom="$1"
              >
                Edit Bill
              </H2>
              <Text
                color={colors.textSecondary}
                fontSize={14}
                fontFamily="$body"
              >
                Update billing information
              </Text>
            </YStack>
          </XStack>
        </XStack>
      </YStack>

      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={{ flex: 1 }}
      >
        <ScrollView 
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{ paddingBottom: spacing.xl }}
        >
          {/* Image Upload Section */}
          <Card
            backgroundColor={colors.card}
            borderRadius={borderRadius.medium}
            padding={spacing.md}
            marginHorizontal={spacing.lg}
            marginTop={spacing.md}
            borderWidth={1}
            borderColor={colors.border}
          >
            <Text
              fontSize={16}
              fontWeight="600"
              color={colors.text}
              fontFamily="$body"
              marginBottom="$3"
            >
              Bill Image
            </Text>
            
            {imageUri ? (
              <YStack space="$3">
                <TouchableOpacity
                  onPress={() => setImageZoomVisible(true)}
                  activeOpacity={0.8}
                >
                  <Image
                    source={{ uri: imageUri }}
                    style={{
                      width: '100%',
                      height: 200,
                      borderRadius: borderRadius.medium,
                      backgroundColor: colors.backgroundAlt,
                    }}
                    resizeMode="cover"
                  />
                  <View
                    style={{
                      position: 'absolute',
                      top: 8,
                      right: 8,
                      backgroundColor: 'rgba(0, 0, 0, 0.6)',
                      borderRadius: 12,
                      padding: 6,
                    }}
                  >
                    <ZoomIcon size={14} color="white" />
                  </View>
                </TouchableOpacity>
                <XStack space="$2">
                  <Button
                    flex={1}
                    onPress={pickImage}
                    style={{
                      backgroundColor: colors.backgroundAlt,
                      borderColor: colors.border,
                      borderWidth: 1,
                    }}
                  >
                    <XStack alignItems="center" space="$2">
                      <CameraIcon size={16} color={colors.textSecondary} />
                      <Text style={{ color: colors.textSecondary, fontSize: 14 }}>Change</Text>
                    </XStack>
                  </Button>
                  <Button
                    flex={1}
                    onPress={() => setImageUri(null)}
                    style={{
                      backgroundColor: colors.backgroundAlt,
                      borderColor: colors.border,
                      borderWidth: 1,
                    }}
                  >
                    <Text style={{ color: colors.textSecondary, fontSize: 14 }}>Remove</Text>
                  </Button>
                </XStack>
              </YStack>
            ) : (
              <TouchableOpacity
                onPress={pickImage}
                style={{
                  height: 120,
                  borderRadius: borderRadius.medium,
                  borderWidth: 2,
                  borderColor: colors.border,
                  borderStyle: 'dashed',
                  backgroundColor: colors.backgroundAlt,
                  justifyContent: 'center',
                  alignItems: 'center',
                }}
              >
                <YStack alignItems="center" space="$2">
                  <CameraIcon size={32} color={colors.textTertiary} />
                  <Text color={colors.textSecondary} fontSize={14} textAlign="center">
                    Tap to add bill image
                  </Text>
                  <Text color={colors.textTertiary} fontSize={12} textAlign="center">
                    Take photo or choose from gallery
                  </Text>
                </YStack>
              </TouchableOpacity>
            )}
          </Card>

          {/* Form Fields */}
          <YStack space="$4" padding={spacing.lg}>
            {/* Customer Name */}
            <YStack space="$2">
              <Text
                fontSize={14}
                fontWeight="600"
                color={colors.text}
                fontFamily="$body"
              >
                Customer Name *
              </Text>
              <Input
                placeholder="Enter customer name"
                value={customerName}
                onChangeText={setCustomerName}
                backgroundColor={colors.card}
                borderColor={colors.border}
                borderRadius={borderRadius.input}
                paddingHorizontal={spacing.md}
                paddingVertical={spacing.sm}
                fontSize={16}
                focusStyle={{
                  borderColor: colors.primary,
                }}
              />
            </YStack>

            {/* Phone Number */}
            <YStack space="$2">
              <Text
                fontSize={14}
                fontWeight="600"
                color={colors.text}
                fontFamily="$body"
              >
                Phone Number *
              </Text>
              <Input
                placeholder="Enter phone number"
                value={phoneNumber}
                onChangeText={setPhoneNumber}
                keyboardType="phone-pad"
                backgroundColor={colors.card}
                borderColor={colors.border}
                borderRadius={borderRadius.input}
                paddingHorizontal={spacing.md}
                paddingVertical={spacing.sm}
                fontSize={16}
                focusStyle={{
                  borderColor: colors.primary,
                }}
              />
            </YStack>

            {/* Bill Number */}
            <YStack space="$2">
              <Text
                fontSize={14}
                fontWeight="600"
                color={colors.text}
                fontFamily="$body"
              >
                Bill Number *
              </Text>
              <Input
                placeholder="Enter bill number"
                value={billNumber}
                onChangeText={setBillNumber}
                backgroundColor={colors.card}
                borderColor={colors.border}
                borderRadius={borderRadius.input}
                paddingHorizontal={spacing.md}
                paddingVertical={spacing.sm}
                fontSize={16}
                focusStyle={{
                  borderColor: colors.primary,
                }}
              />
            </YStack>

            {/* Delivery Date */}
            <DatePickerInput
              label="Delivery Date"
              value={deliveryDate}
              onDateChange={setDeliveryDate}
              placeholder="Select delivery date"
              required={true}
            />

            {/* Notes */}
            <YStack space="$2">
              <Text
                fontSize={14}
                fontWeight="600"
                color={colors.text}
                fontFamily="$body"
              >
                Notes
              </Text>
              <TextArea
                placeholder="Additional notes (optional)"
                value={notes}
                onChangeText={setNotes}
                numberOfLines={4}
                backgroundColor={colors.card}
                borderColor={colors.border}
                borderRadius={borderRadius.input}
                paddingHorizontal={spacing.md}
                paddingVertical={spacing.sm}
                fontSize={16}
                minHeight={100}
                textAlignVertical="top"
                focusStyle={{
                  borderColor: colors.primary,
                }}
              />
            </YStack>

            {/* Action Buttons */}
            <YStack space="$3" marginTop="$6">
              <Button
                onPress={handleSubmit}
                disabled={loading || uploadingImage}
                style={{
                  backgroundColor: colors.primary,
                  borderRadius: borderRadius.button,
                  paddingVertical: spacing.md,
                  height: 50,
                }}
                pressStyle={{
                  backgroundColor: colors.primaryDark,
                  scale: 0.98,
                }}
                opacity={loading || uploadingImage ? 0.6 : 1}
              >
                <XStack alignItems="center" space="$2">
                  <SaveIcon size={18} color="white" />
                  <Text
                    style={{
                      color: 'white',
                      fontSize: 16,
                      fontWeight: '600',
                    }}
                  >
                    {uploadingImage ? 'Uploading Image...' : loading ? 'Updating Bill...' : 'Update Bill'}
                  </Text>
                </XStack>
              </Button>

              <Button
                onPress={() => navigation.goBack()}
                style={{
                  backgroundColor: 'transparent',
                  borderColor: colors.border,
                  borderWidth: 1,
                  borderRadius: borderRadius.button,
                  paddingVertical: spacing.md,
                  height: 50,
                }}
                pressStyle={{
                  backgroundColor: colors.backgroundAlt,
                  scale: 0.98,
                }}
              >
                <Text
                  style={{
                    color: colors.text,
                    fontSize: 16,
                    fontWeight: '500',
                  }}
                >
                  Cancel
                </Text>
              </Button>
            </YStack>
          </YStack>
        </ScrollView>
      </KeyboardAvoidingView>

      {/* Image Zoom Modal */}
      {imageUri && (
        <ImageZoomModal
          visible={imageZoomVisible}
          imageUrl={imageUri}
          onClose={() => setImageZoomVisible(false)}
        />
      )}
    </YStack>
  );
};

export default EditBillScreen;
