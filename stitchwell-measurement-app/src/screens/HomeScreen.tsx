import React, { useState, useEffect } from 'react';
import { FlatList, View, ActivityIndicator, Linking, Alert } from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { RootStackParamList } from '../navigation/types';
import {
  Button,
  H1,
  Paragraph,
  XStack,
  YStack,
  Card,
  Text,
  Input,
  Separator,
} from 'tamagui';
import {
  UserIcon,
  SearchIcon,
  CalendarIcon,
  FileTextIcon,
  FilterIcon,
  TrashIcon,
  CallIcon
} from '../components/TabIcons';
import GradientWrapper from '../components/GradientWrapper';
import FloatingActionButton from '../components/FloatingActionButton';
import { Measurement } from '../types/index';
import { colors, shadows, borderRadius, spacing } from '../theme/colors';
import { subscribeMeasurements, deleteMeasurement, testFirebaseConnection, searchAllMeasurements } from '../services/measurementService';

type Props = NativeStackScreenProps<RootStackParamList, 'Home'>;

const HomeScreen = ({ navigation }: Props) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [measurements, setMeasurements] = useState<Measurement[]>([]);
  const [searchResults, setSearchResults] = useState<Measurement[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [allLoaded, setAllLoaded] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Define types for the subscription functions
  type SubscriptionFunctions = {
    unsubscribe: () => void;
    loadMore: () => void;
  };

  // Reference to store the loadMore function
  const measurementsRef = React.useRef<SubscriptionFunctions | null>(null);

  // Handle pull-to-refresh
  const handleRefresh = () => {
    setRefreshing(true);
    // The real-time listener will update the data
    // Just reset the refreshing state after a short delay
    setTimeout(() => {
      setRefreshing(false);
    }, 1000);
  };

  // Handle delete measurement
  const handleDelete = async (id: string) => {
    try {
      await deleteMeasurement(id);
      // No need to update state as the listener will handle it
    } catch (error) {
      console.error('Error deleting measurement:', error);
      setError('Failed to delete measurement. Please try again.');
    }
  };

  // Handle phone call
  const handleCall = async (phoneNumber: string) => {
    try {
      const phoneUrl = `tel:${phoneNumber}`;
      const canOpen = await Linking.canOpenURL(phoneUrl);

      if (canOpen) {
        await Linking.openURL(phoneUrl);
      } else {
        Alert.alert('Error', 'Unable to make phone calls on this device');
      }
    } catch (error) {
      console.error('Error making phone call:', error);
      Alert.alert('Error', 'Failed to initiate phone call');
    }
  };

  // Subscribe to real-time updates from Firestore with pagination
  useEffect(() => {
    const initializeData = async () => {
      setLoading(true);
      setError(null);

      // First test Firebase connection
      console.log('🔄 Initializing measurements data...');
      const isConnected = await testFirebaseConnection();

      if (!isConnected) {
        setError('Unable to connect to the database. Please check your internet connection and try again.');
        setLoading(false);
        return;
      }

      // Set up real-time listener with pagination
      const subscription = subscribeMeasurements(
        (updatedMeasurements: Measurement[], paginationInfo: { isLoading: boolean, allLoaded: boolean }) => {
          console.log('📱 Updating UI with measurements:', updatedMeasurements.length);
          setMeasurements(updatedMeasurements);
          setLoading(false);
          setLoadingMore(paginationInfo.isLoading);
          setAllLoaded(paginationInfo.allLoaded);
          setError(null);
        },
        15, // pageSize - number of items to load per page
        (error: Error) => {
          console.error('❌ Error in measurements subscription:', error);
          let errorMessage = 'Failed to load measurements. Please try again.';

          if (error.message.includes('timeout')) {
            errorMessage = 'Connection timeout. Please check your internet connection and try again.';
          } else if (error.message.includes('permission')) {
            errorMessage = 'Permission denied. Please check your database access.';
          } else if (error.message.includes('network')) {
            errorMessage = 'Network error. Please check your internet connection.';
          }

          setError(errorMessage);
          setLoading(false);
          setLoadingMore(false);
        }
      ) as SubscriptionFunctions;

      // Store the subscription functions in the ref for later use
      measurementsRef.current = subscription;
    };

    initializeData();

    // Cleanup subscription on unmount
    return () => {
      if (measurementsRef.current) {
        measurementsRef.current.unsubscribe();
      }
    };
  }, []);

  // Effect to handle search across all measurements
  useEffect(() => {
    const performSearch = async () => {
      if (searchQuery.trim() === '') {
        setSearchResults([]);
        setIsSearching(false);
        return;
      }

      setIsSearching(true);
      try {
        console.log('🔍 Performing search for:', searchQuery);
        const results = await searchAllMeasurements(searchQuery);
        setSearchResults(results);
        console.log('✅ Search completed, found:', results.length, 'results');
      } catch (error) {
        console.error('❌ Search error:', error);
        setSearchResults([]);
      } finally {
        setIsSearching(false);
      }
    };

    // Debounce search to avoid too many API calls
    const timeoutId = setTimeout(performSearch, 300);
    return () => clearTimeout(timeoutId);
  }, [searchQuery]);

  // Filter and sort measurements based on search query
  const filteredMeasurements = React.useMemo(() => {
    // If user is searching, use search results from entire database
    if (searchQuery.trim() !== '') {
      // Return search results (already sorted by timestamp in searchAllMeasurements)
      return searchResults;
    }

    // If not searching, use the loaded 2025 measurements with smart sorting
    let filtered = measurements;

    // For non-search results, apply smart delivery date priority sorting
    return filtered.sort((a, b) => {
      // Reference date: May 2025
      const currentMonth = new Date(2025, 4, 1); // May 2025
      const currentMonthEnd = new Date(2025, 4, 31, 23, 59, 59); // End of May 2025

      // Parse dates (DD-MM-YYYY format)
      const parseDate = (dateStr: string) => {
        if (!dateStr) return null;
        const [day, month, year] = dateStr.split('-');
        return new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
      };

      const deliveryA = parseDate(a.deliveryDate);
      const deliveryB = parseDate(b.deliveryDate);

      // Determine priority categories
      const getPriority = (deliveryDate: Date | null) => {
        if (!deliveryDate) return 4; // No delivery date - lowest priority
        if (deliveryDate >= currentMonth && deliveryDate <= currentMonthEnd) return 1; // Current month (May 2025) - highest priority
        if (deliveryDate > currentMonthEnd) return 2; // Future deliveries - second priority
        return 3; // Overdue deliveries - third priority (old dates)
      };

      const priorityA = getPriority(deliveryA);
      const priorityB = getPriority(deliveryB);

      // Sort by priority first
      if (priorityA !== priorityB) {
        return priorityA - priorityB;
      }

      // Within same priority, apply specific sorting rules
      if (priorityA === 1 || priorityA === 2) {
        // For current month and future deliveries, sort by delivery date (earliest first)
        if (deliveryA && deliveryB) {
          return deliveryA.getTime() - deliveryB.getTime();
        }
      }

      if (priorityA === 3) {
        // For overdue deliveries, sort by timestamp (newest first) to prioritize recent orders
        const timestampA = a.timestamp || 0;
        const timestampB = b.timestamp || 0;
        return timestampB - timestampA;
      }

      if (priorityA === 4) {
        // For items without delivery dates, sort by timestamp (newest first)
        const timestampA = a.timestamp || 0;
        const timestampB = b.timestamp || 0;
        return timestampB - timestampA;
      }

      return 0;
    });
  }, [measurements, searchQuery, searchResults]);

  // Render each measurement item
  const renderMeasurementItem = ({ item, index }: { item: Measurement, index: number }) => {
    // Get the first three measurements to display as preview
    const measurementEntries = item.measurements
      ? Object.entries(item.measurements)
          .filter(([_, value]) => value !== undefined && value !== null)
          .slice(0, 3)
      : [];

    // Determine delivery status for visual indicators (relative to May 2025)
    const getDeliveryStatus = () => {
      if (!item.deliveryDate) return 'none';

      // Reference date: May 2025
      const currentMonth = new Date(2025, 4, 1); // May 2025 (month is 0-indexed)
      const currentMonthEnd = new Date(2025, 4, 31, 23, 59, 59); // End of May 2025

      const [day, month, year] = item.deliveryDate.split('-');
      const deliveryDate = new Date(parseInt(year), parseInt(month) - 1, parseInt(day));

      if (deliveryDate < currentMonth) return 'overdue'; // Before May 2025
      if (deliveryDate >= currentMonth && deliveryDate <= currentMonthEnd) return 'current'; // May 2025
      return 'future'; // After May 2025
    };

    const deliveryStatus = getDeliveryStatus();

    // Choose gradient and colors based on delivery status
    let cardGradient: string[];
    let statusColor: string;

    switch (deliveryStatus) {
      case 'overdue':
        cardGradient = ['#EF4444', '#DC2626']; // Red gradient for overdue (before May 2025)
        statusColor = colors.error;
        break;
      case 'current':
        cardGradient = ['#F59E0B', '#D97706']; // Orange gradient for current month (May 2025)
        statusColor = colors.warning;
        break;
      default:
        // Use original gradient cycling for future deliveries and items without dates
        const gradientKey = `cardGradient${(index % 3) + 1}` as keyof typeof colors;
        cardGradient = colors[gradientKey] as string[];
        statusColor = cardGradient[0];
    }

    return (
      <Card
        elevate
        size="$3"
        marginVertical="$2"
        scale={0.98}
        hoverStyle={{ scale: 1 }}
        pressStyle={{ scale: 0.96 }}
        onPress={() => {
          // Navigate to bill detail view
          navigation.navigate('BillDetail', { billId: item.id });
        }}
        style={{
          backgroundColor: colors.card,
          borderRadius: borderRadius.card,
          overflow: 'hidden',
          ...shadows.medium,
        }}
      >
        <GradientWrapper
          colors={cardGradient}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
          style={{
            paddingVertical: spacing.xs,
            paddingHorizontal: spacing.sm,
          }}
        >
          <XStack justifyContent="space-between" alignItems="center">
            <XStack gap="$1" alignItems="center" flex={1}>
              <FileTextIcon size={16} color="white" />
              <Text
                fontWeight="700"
                color="white"
                fontSize={14}
                fontFamily="$heading"
                letterSpacing={0.2}
                numberOfLines={1}
                ellipsizeMode="tail"
              >
                {item.billNo ? `Bill #${item.billNo}` : `Measurement #${item.id.substring(0, 6)}`}
              </Text>
            </XStack>
            <Button
              size="$1"
              chromeless
              circular
              onPress={(e) => {
                e.stopPropagation(); // Prevent card onPress from firing
                handleDelete(item.id);
              }}
              pressStyle={{ opacity: 0.5 }}
              hoverStyle={{ opacity: 0.8 }}
              style={{
                width: 28,
                height: 28,
                minHeight: 28,
              }}
            >
              <TrashIcon size={14} color="white" />
            </Button>
          </XStack>
        </GradientWrapper>

        <YStack padding="$3" gap="$2">
          <XStack gap="$2" alignItems="center" justifyContent="space-between">
            <XStack gap="$2" alignItems="center" flex={1}>
              <View style={{
                width: 32,
                height: 32,
                borderRadius: 16,
                backgroundColor: `${cardGradient[0]}15`,
                justifyContent: 'center',
                alignItems: 'center',
              }}>
                <UserIcon size={16} color={cardGradient[0]} />
              </View>
              <YStack flex={1}>
                <Text
                  color={colors.text}
                  fontWeight="600"
                  fontSize={16}
                  fontFamily="$heading"
                  lineHeight={18}
                  numberOfLines={1}
                  ellipsizeMode="tail"
                >
                  {item.name}
                </Text>
                <Text
                  color={colors.textSecondary}
                  fontSize={13}
                  fontFamily="$body"
                  fontWeight="400"
                  lineHeight={16}
                >
                  {item.phoneNo}
                </Text>
              </YStack>
            </XStack>
            {item.phoneNo && (
              <Button
                size="$2"
                chromeless
                circular
                onPress={(e) => {
                  e.stopPropagation();
                  handleCall(item.phoneNo);
                }}
                pressStyle={{ opacity: 0.5 }}
                hoverStyle={{ opacity: 0.8 }}
                style={{
                  width: 32,
                  height: 32,
                  backgroundColor: `${cardGradient[0]}15`,
                  borderWidth: 1,
                  borderColor: `${cardGradient[0]}30`,
                }}
              >
                <CallIcon size={16} color={cardGradient[0]} />
              </Button>
            )}
          </XStack>

          <XStack gap="$2" alignItems="center">
            <View style={{
              width: 28,
              height: 28,
              borderRadius: 14,
              backgroundColor: `${cardGradient[0]}15`,
              justifyContent: 'center',
              alignItems: 'center',
            }}>
              <CalendarIcon size={14} color={cardGradient[0]} />
            </View>
            <XStack gap="$3" alignItems="center" flex={1}>
              <Text
                color={colors.textSecondary}
                fontSize={13}
                fontFamily="$body"
                fontWeight="500"
                lineHeight={15}
              >
                {item.date}
              </Text>
              {item.deliveryDate && (
                <>
                  <Text color={colors.textTertiary} fontSize={12}>•</Text>
                  <XStack alignItems="center" gap="$1">
                    <Text
                      color={statusColor}
                      fontSize={12}
                      fontFamily="$body"
                      fontWeight="500"
                      lineHeight={14}
                    >
                      Delivery: {item.deliveryDate}
                    </Text>
                    {deliveryStatus === 'overdue' && (
                      <View style={{
                        width: 6,
                        height: 6,
                        borderRadius: 3,
                        backgroundColor: colors.error,
                      }} />
                    )}
                    {deliveryStatus === 'current' && (
                      <View style={{
                        width: 6,
                        height: 6,
                        borderRadius: 3,
                        backgroundColor: colors.warning,
                      }} />
                    )}
                  </XStack>
                </>
              )}
            </XStack>
          </XStack>

          {measurementEntries.length > 0 && <Separator marginVertical="$1" />}

          {measurementEntries.length > 0 && (
            <XStack flexWrap="wrap" gap="$1">
              {measurementEntries.map(([key, value], idx) => (
                <View
                  key={idx}
                  style={{
                    backgroundColor: `${cardGradient[0]}10`,
                    paddingHorizontal: spacing.xs,
                    paddingVertical: 2,
                    borderRadius: borderRadius.tag - 2,
                    borderWidth: 1,
                    borderColor: `${cardGradient[0]}20`,
                  }}
                >
                  <Text
                    color={cardGradient[0]}
                    fontSize={11}
                    fontWeight="500"
                    fontFamily="$body"
                    lineHeight={14}
                  >
                    {key.charAt(0).toUpperCase() + key.slice(1)}: {value}
                  </Text>
                </View>
              ))}
            </XStack>
          )}

          {item.notes && (
            <>
              <Separator marginVertical="$1" />
              <XStack gap="$2" alignItems="center">
                <View style={{
                  width: 24,
                  height: 24,
                  borderRadius: 12,
                  backgroundColor: `${cardGradient[0]}15`,
                  justifyContent: 'center',
                  alignItems: 'center',
                }}>
                  <FileTextIcon size={12} color={cardGradient[0]} />
                </View>
                <Text
                  color={colors.textSecondary}
                  numberOfLines={2}
                  ellipsizeMode="tail"
                  fontFamily="$body"
                  fontSize={12}
                  lineHeight={16}
                  flex={1}
                >
                  {item.notes}
                </Text>
              </XStack>
            </>
          )}
        </YStack>
      </Card>
    );
  };

  return (
    <YStack f={1} style={{ backgroundColor: colors.background }}>
      <GradientWrapper
        colors={colors.primaryGradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={{
          paddingHorizontal: spacing.lg,
          paddingTop: spacing.xl,
          paddingBottom: spacing.lg,
          borderBottomLeftRadius: borderRadius.medium,
          borderBottomRightRadius: borderRadius.medium,
          ...shadows.medium,
        }}
      >
        <H1
          marginBottom="$1"
          color={colors.textLight}
          fontSize={34}
          fontWeight="700"
          fontFamily="$heading"
          letterSpacing={-1}
        >
          Measurements
        </H1>
        <Paragraph
          marginBottom="$4"
          color="rgba(255, 255, 255, 0.85)"
          fontSize={16}
          fontWeight="400"
          fontFamily="$body"
          lineHeight={22}
        >
          Manage your tailoring business with precision
        </Paragraph>

        <XStack gap="$3" marginBottom="$2">
          <XStack
            flex={1}
            alignItems="center"
            backgroundColor="rgba(255, 255, 255, 0.15)"
            borderRadius={borderRadius.pill}
            paddingHorizontal={spacing.md}
            style={{
              height: 48,
              ...shadows.small,
            }}
          >
            {isSearching ? (
              <ActivityIndicator size="small" color="rgba(255, 255, 255, 0.8)" />
            ) : (
              <SearchIcon size={18} color="rgba(255, 255, 255, 0.8)" />
            )}
            <Input
              flex={1}
              placeholder={searchQuery.trim() !== '' ? "Searching entire database..." : "Search by name, phone, or bill number"}
              value={searchQuery}
              onChangeText={setSearchQuery}
              inputMode="text"
              borderColor="transparent"
              backgroundColor="transparent"
              placeholderTextColor="rgba(255, 255, 255, 0.6)"
              color={colors.textLight}
              fontFamily="$body"
              fontSize={16}
              fontWeight="400"
              marginLeft="$2"
              style={{
                height: 48,
                paddingHorizontal: 0,
              }}
            />
          </XStack>
          <Button
            icon={FilterIcon}
            circular
            size="$4"
            backgroundColor="rgba(255, 255, 255, 0.15)"
            borderColor="transparent"
            hoverStyle={{ backgroundColor: 'rgba(255, 255, 255, 0.25)' }}
            pressStyle={{ backgroundColor: 'rgba(255, 255, 255, 0.1)' }}
            style={{
              width: 48,
              height: 48,
              ...shadows.small,
            }}
          />
        </XStack>
      </GradientWrapper>

      {loading ? (
        <YStack flex={1} justifyContent="center" alignItems="center" padding="$6">
          <ActivityIndicator size="large" color={colors.primary} />
          <Text
            color={colors.textSecondary}
            marginTop="$4"
            fontSize={16}
            fontFamily="$body"
            fontWeight="500"
          >
            Loading measurements...
          </Text>
        </YStack>
      ) : error ? (
        <YStack
          alignItems="center"
          padding="$8"
          marginTop="$8"
          marginHorizontal={spacing.lg}
          backgroundColor={colors.card}
          borderRadius={borderRadius.card}
          style={{
            ...shadows.medium,
            borderWidth: 1,
            borderColor: `${colors.error}20`,
          }}
        >
          <View
            style={{
              width: 60,
              height: 60,
              borderRadius: 30,
              backgroundColor: `${colors.error}15`,
              justifyContent: 'center',
              alignItems: 'center',
              marginBottom: spacing.md,
            }}
          >
            <Text fontSize={24}>⚠️</Text>
          </View>
          <Text
            color={colors.text}
            fontSize={18}
            fontWeight="600"
            marginBottom="$2"
            fontFamily="$heading"
          >
            Something went wrong
          </Text>
          <Text
            color={colors.textSecondary}
            textAlign="center"
            marginBottom="$4"
            fontFamily="$body"
            lineHeight={20}
          >
            {error}
          </Text>
          <Button
            marginTop="$4"
            backgroundColor={colors.primary}
            color={colors.textLight}
            borderRadius={borderRadius.button}
            paddingHorizontal={spacing.lg}
            height={48}
            onPress={async () => {
              setLoading(true);
              setError(null);
              setAllLoaded(false);
              setLoadingMore(false);

              // First test Firebase connection
              console.log('🔄 Retrying measurements data...');
              const isConnected = await testFirebaseConnection();

              if (!isConnected) {
                setError('Unable to connect to the database. Please check your internet connection and try again.');
                setLoading(false);
                return;
              }

              // Re-initialize the subscription with pagination
              const subscription = subscribeMeasurements(
                (updatedMeasurements: Measurement[], paginationInfo: { isLoading: boolean, allLoaded: boolean }) => {
                  console.log('📱 Retry: Updating UI with measurements:', updatedMeasurements.length);
                  setMeasurements(updatedMeasurements);
                  setLoading(false);
                  setLoadingMore(paginationInfo.isLoading);
                  setAllLoaded(paginationInfo.allLoaded);
                  setError(null);
                },
                15, // pageSize
                (error: Error) => {
                  console.error('❌ Retry: Error in measurements subscription:', error);
                  let errorMessage = 'Failed to load measurements. Please try again.';

                  if (error.message.includes('timeout')) {
                    errorMessage = 'Connection timeout. Please check your internet connection and try again.';
                  } else if (error.message.includes('permission')) {
                    errorMessage = 'Permission denied. Please check your database access.';
                  } else if (error.message.includes('network')) {
                    errorMessage = 'Network error. Please check your internet connection.';
                  }

                  setError(errorMessage);
                  setLoading(false);
                  setLoadingMore(false);
                }
              ) as SubscriptionFunctions;

              // Update the ref
              measurementsRef.current = subscription;
            }}
          >
            Try Again
          </Button>
        </YStack>
      ) : (
        <FlatList
          data={filteredMeasurements}
          renderItem={renderMeasurementItem}
          keyExtractor={item => item.id}
          showsVerticalScrollIndicator={false}
          refreshing={refreshing}
          onRefresh={handleRefresh}
          onEndReached={() => {
            if (!loadingMore && !allLoaded && measurementsRef.current) {
              measurementsRef.current.loadMore();
            }
          }}
          onEndReachedThreshold={0.5} // Trigger when user scrolls to 50% of the end
          contentContainerStyle={{
            paddingHorizontal: spacing.lg,
            paddingTop: spacing.md,
            paddingBottom: spacing.xxl * 2,
          }}
          ListFooterComponent={
            loadingMore ? (
              <YStack padding="$6" alignItems="center">
                <ActivityIndicator size="small" color={colors.primary} />
                <Text
                  marginTop="$2"
                  color={colors.textSecondary}
                  fontSize={14}
                  fontFamily="$body"
                  fontWeight="500"
                >
                  Loading more measurements...
                </Text>
              </YStack>
            ) : allLoaded && measurements.length > 0 ? (
              <YStack padding="$6" alignItems="center">
                <View
                  style={{
                    width: 40,
                    height: 40,
                    borderRadius: 20,
                    backgroundColor: `${colors.primary}10`,
                    justifyContent: 'center',
                    alignItems: 'center',
                    marginBottom: spacing.sm,
                  }}
                >
                  <Text fontSize={18}>✓</Text>
                </View>
                <Text
                  color={colors.textSecondary}
                  fontSize={14}
                  fontFamily="$body"
                  fontWeight="500"
                >
                  You've reached the end
                </Text>
              </YStack>
            ) : null
          }
          ListEmptyComponent={
            <YStack
              alignItems="center"
              padding="$8"
              marginTop="$8"
              marginHorizontal={spacing.lg}
              backgroundColor={colors.card}
              borderRadius={borderRadius.card}
              style={{
                ...shadows.medium,
                borderWidth: 1,
                borderColor: colors.border,
              }}
            >
              <View
                style={{
                  width: 60,
                  height: 60,
                  borderRadius: 30,
                  backgroundColor: `${colors.primary}10`,
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginBottom: spacing.md,
                }}
              >
                <Text fontSize={24}>📋</Text>
              </View>
              <Text
                color={colors.text}
                fontSize={18}
                fontWeight="600"
                marginBottom="$2"
                fontFamily="$heading"
              >
                No measurements yet
              </Text>
              <Text
                color={colors.textSecondary}
                textAlign="center"
                fontFamily="$body"
                lineHeight={20}
              >
                Add your first measurement by tapping the + button
              </Text>
            </YStack>
          }
        />
      )}

      <FloatingActionButton
        onPress={() => {
          // Navigate to the new AddBill screen
          navigation.navigate('AddBill');
        }}
      />
    </YStack>
  );
};

export default HomeScreen;
