import React, { useState, useEffect } from 'react';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { RootStackParamList } from '../navigation/types';
import {
  Button,
  H2,
  XStack,
  YStack,
  Text,
  Card,
  Separator,
} from 'tamagui';
import {
  ScrollView,
  View,
  Alert,
  TouchableOpacity,
  Image,
  ActivityIndicator,
  ActionSheetIOS,
  Platform
} from 'react-native';
import { Measurement } from '../types';
import { colors, borderRadius, spacing } from '../theme/colors';
import { getMeasurementById, deleteMeasurement } from '../services/measurementService';
import {
  UserIcon,
  PhoneIcon,
  BillIcon,
  CalendarIcon,
  FileTextIcon,
  EditIcon,
  TrashIcon,
  BackIcon,
  CallIcon,
  ImageIcon,
  ZoomIcon
} from '../components/TabIcons';
import ImageZoomModal from '../components/ImageZoomModal';

type Props = NativeStackScreenProps<RootStackParamList, 'BillDetail'>;

const BillDetailScreen = ({ navigation, route }: Props) => {
  const { billId } = route.params;
  const [bill, setBill] = useState<Measurement | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [imageZoomVisible, setImageZoomVisible] = useState(false);

  useEffect(() => {
    loadBillDetails();
  }, [billId]);

  const loadBillDetails = async () => {
    try {
      setLoading(true);
      const billData = await getMeasurementById(billId);
      setBill(billData);
      setError(null);
    } catch (err) {
      console.error('Error loading bill details:', err);
      setError('Failed to load bill details');
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = () => {
    navigation.navigate('EditBill', { billId });
  };

  const handleDelete = () => {
    const showDeleteConfirmation = () => {
      if (Platform.OS === 'ios') {
        ActionSheetIOS.showActionSheetWithOptions(
          {
            options: ['Cancel', 'Delete Bill'],
            destructiveButtonIndex: 1,
            cancelButtonIndex: 0,
            title: 'Are you sure you want to delete this bill?',
            message: 'This action cannot be undone.',
          },
          (buttonIndex) => {
            if (buttonIndex === 1) {
              confirmDelete();
            }
          }
        );
      } else {
        Alert.alert(
          'Delete Bill',
          'Are you sure you want to delete this bill? This action cannot be undone.',
          [
            { text: 'Cancel', style: 'cancel' },
            { text: 'Delete', style: 'destructive', onPress: confirmDelete },
          ]
        );
      }
    };

    const confirmDelete = async () => {
      try {
        await deleteMeasurement(billId);
        Alert.alert('Success', 'Bill deleted successfully', [
          { text: 'OK', onPress: () => navigation.goBack() }
        ]);
      } catch (err) {
        console.error('Error deleting bill:', err);
        Alert.alert('Error', 'Failed to delete bill. Please try again.');
      }
    };

    showDeleteConfirmation();
  };

  const handleCall = (phoneNumber: string) => {
    const url = `tel:${phoneNumber}`;
    Alert.alert(
      'Call Customer',
      `Do you want to call ${phoneNumber}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Call', onPress: () => {
          // In a real app, you would use Linking.openURL(url)
          console.log('Calling:', phoneNumber);
        }},
      ]
    );
  };

  if (loading) {
    return (
      <YStack f={1} backgroundColor={colors.background} justifyContent="center" alignItems="center">
        <ActivityIndicator size="large" color={colors.primary} />
        <Text marginTop="$4" color={colors.textSecondary}>Loading bill details...</Text>
      </YStack>
    );
  }

  if (error || !bill) {
    return (
      <YStack f={1} backgroundColor={colors.background} justifyContent="center" alignItems="center" padding={spacing.lg}>
        <Text color={colors.error} fontSize={16} textAlign="center" marginBottom="$4">
          {error || 'Bill not found'}
        </Text>
        <Button onPress={() => navigation.goBack()}>
          <Text>Go Back</Text>
        </Button>
      </YStack>
    );
  }

  return (
    <YStack f={1} backgroundColor={colors.background}>
      {/* Header */}
      <YStack 
        backgroundColor={colors.card}
        paddingHorizontal={spacing.lg}
        paddingTop={spacing.xl}
        paddingBottom={spacing.md}
        borderBottomWidth={1}
        borderBottomColor={colors.border}
      >
        <XStack alignItems="center" justifyContent="space-between" marginBottom="$3">
          <XStack alignItems="center" space="$3">
            <TouchableOpacity
              onPress={() => navigation.goBack()}
              style={{
                width: 32,
                height: 32,
                borderRadius: 16,
                backgroundColor: colors.backgroundAlt,
                justifyContent: 'center',
                alignItems: 'center',
              }}
            >
              <BackIcon size={18} color={colors.text} />
            </TouchableOpacity>
            <YStack>
              <H2
                color={colors.text}
                fontSize={24}
                fontWeight="700"
                fontFamily="$heading"
                marginBottom="$1"
              >
                Bill Details
              </H2>
              <Text
                color={colors.textSecondary}
                fontSize={14}
                fontFamily="$body"
              >
                {bill.billNo ? `Bill #${bill.billNo}` : `ID: ${bill.id.substring(0, 8)}`}
              </Text>
            </YStack>
          </XStack>
          
          <XStack space="$2">
            <TouchableOpacity
              onPress={handleEdit}
              style={{
                width: 40,
                height: 40,
                borderRadius: 20,
                backgroundColor: colors.primary,
                justifyContent: 'center',
                alignItems: 'center',
              }}
            >
              <EditIcon size={18} color="white" />
            </TouchableOpacity>
            <TouchableOpacity
              onPress={handleDelete}
              style={{
                width: 40,
                height: 40,
                borderRadius: 20,
                backgroundColor: colors.error,
                justifyContent: 'center',
                alignItems: 'center',
              }}
            >
              <TrashIcon size={18} color="white" />
            </TouchableOpacity>
          </XStack>
        </XStack>
      </YStack>

      <ScrollView 
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ paddingBottom: spacing.xl }}
      >
        {/* Customer Information */}
        <Card
          backgroundColor={colors.card}
          borderRadius={borderRadius.medium}
          padding={spacing.lg}
          marginHorizontal={spacing.lg}
          marginTop={spacing.md}
          borderWidth={1}
          borderColor={colors.border}
        >
          <Text
            fontSize={16}
            fontWeight="600"
            color={colors.text}
            fontFamily="$body"
            marginBottom="$4"
          >
            Customer Information
          </Text>
          
          <YStack space="$3">
            <XStack alignItems="center" justifyContent="space-between">
              <XStack alignItems="center" space="$3" flex={1}>
                <View style={{
                  width: 40,
                  height: 40,
                  borderRadius: 20,
                  backgroundColor: `${colors.primary}15`,
                  justifyContent: 'center',
                  alignItems: 'center',
                }}>
                  <UserIcon size={20} color={colors.primary} />
                </View>
                <YStack flex={1}>
                  <Text
                    color={colors.text}
                    fontWeight="600"
                    fontSize={16}
                    fontFamily="$heading"
                  >
                    {bill.name}
                  </Text>
                  <Text
                    color={colors.textSecondary}
                    fontSize={14}
                    fontFamily="$body"
                  >
                    Customer Name
                  </Text>
                </YStack>
              </XStack>
            </XStack>

            <Separator />

            <XStack alignItems="center" justifyContent="space-between">
              <XStack alignItems="center" space="$3" flex={1}>
                <View style={{
                  width: 40,
                  height: 40,
                  borderRadius: 20,
                  backgroundColor: `${colors.primary}15`,
                  justifyContent: 'center',
                  alignItems: 'center',
                }}>
                  <PhoneIcon size={20} color={colors.primary} />
                </View>
                <YStack flex={1}>
                  <Text
                    color={colors.text}
                    fontWeight="600"
                    fontSize={16}
                    fontFamily="$heading"
                  >
                    {bill.phoneNo}
                  </Text>
                  <Text
                    color={colors.textSecondary}
                    fontSize={14}
                    fontFamily="$body"
                  >
                    Phone Number
                  </Text>
                </YStack>
              </XStack>
              {bill.phoneNo && (
                <TouchableOpacity
                  onPress={() => handleCall(bill.phoneNo)}
                  style={{
                    width: 36,
                    height: 36,
                    borderRadius: 18,
                    backgroundColor: colors.accent,
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}
                >
                  <CallIcon size={16} color="white" />
                </TouchableOpacity>
              )}
            </XStack>
          </YStack>
        </Card>

        {/* Bill Information */}
        <Card
          backgroundColor={colors.card}
          borderRadius={borderRadius.medium}
          padding={spacing.lg}
          marginHorizontal={spacing.lg}
          marginTop={spacing.md}
          borderWidth={1}
          borderColor={colors.border}
        >
          <Text
            fontSize={16}
            fontWeight="600"
            color={colors.text}
            fontFamily="$body"
            marginBottom="$4"
          >
            Bill Information
          </Text>
          
          <YStack space="$3">
            <XStack alignItems="center" space="$3">
              <View style={{
                width: 40,
                height: 40,
                borderRadius: 20,
                backgroundColor: `${colors.secondary}15`,
                justifyContent: 'center',
                alignItems: 'center',
              }}>
                <BillIcon size={20} color={colors.secondary} />
              </View>
              <YStack flex={1}>
                <Text
                  color={colors.text}
                  fontWeight="600"
                  fontSize={16}
                  fontFamily="$heading"
                >
                  {bill.billNo || 'Not specified'}
                </Text>
                <Text
                  color={colors.textSecondary}
                  fontSize={14}
                  fontFamily="$body"
                >
                  Bill Number
                </Text>
              </YStack>
            </XStack>

            <Separator />

            <XStack alignItems="center" space="$3">
              <View style={{
                width: 40,
                height: 40,
                borderRadius: 20,
                backgroundColor: `${colors.secondary}15`,
                justifyContent: 'center',
                alignItems: 'center',
              }}>
                <CalendarIcon size={20} color={colors.secondary} />
              </View>
              <YStack flex={1}>
                <Text
                  color={colors.text}
                  fontWeight="600"
                  fontSize={16}
                  fontFamily="$heading"
                >
                  {bill.deliveryDate || 'Not specified'}
                </Text>
                <Text
                  color={colors.textSecondary}
                  fontSize={14}
                  fontFamily="$body"
                >
                  Delivery Date
                </Text>
              </YStack>
            </XStack>
          </YStack>
        </Card>

        {/* Bill Image */}
        {bill.imageUrl && (
          <Card
            backgroundColor={colors.card}
            borderRadius={borderRadius.medium}
            padding={spacing.lg}
            marginHorizontal={spacing.lg}
            marginTop={spacing.md}
            borderWidth={1}
            borderColor={colors.border}
          >
            <XStack alignItems="center" justifyContent="space-between" marginBottom="$3">
              <Text
                fontSize={16}
                fontWeight="600"
                color={colors.text}
                fontFamily="$body"
              >
                Bill Image
              </Text>
              <TouchableOpacity
                onPress={() => setImageZoomVisible(true)}
                style={{
                  padding: 8,
                  borderRadius: 16,
                  backgroundColor: `${colors.primary}15`,
                }}
              >
                <ZoomIcon size={16} color={colors.primary} />
              </TouchableOpacity>
            </XStack>
            <TouchableOpacity
              onPress={() => setImageZoomVisible(true)}
              activeOpacity={0.8}
            >
              <Image
                source={{ uri: bill.imageUrl }}
                style={{
                  width: '100%',
                  height: 200,
                  borderRadius: borderRadius.medium,
                  backgroundColor: colors.backgroundAlt,
                }}
                resizeMode="cover"
              />
              <View
                style={{
                  position: 'absolute',
                  top: 8,
                  right: 8,
                  backgroundColor: 'rgba(0, 0, 0, 0.6)',
                  borderRadius: 12,
                  padding: 6,
                }}
              >
                <ZoomIcon size={14} color="white" />
              </View>
            </TouchableOpacity>
          </Card>
        )}

        {/* Notes */}
        {bill.notes && (
          <Card
            backgroundColor={colors.card}
            borderRadius={borderRadius.medium}
            padding={spacing.lg}
            marginHorizontal={spacing.lg}
            marginTop={spacing.md}
            borderWidth={1}
            borderColor={colors.border}
          >
            <XStack alignItems="center" space="$3" marginBottom="$3">
              <View style={{
                width: 40,
                height: 40,
                borderRadius: 20,
                backgroundColor: `${colors.accent}15`,
                justifyContent: 'center',
                alignItems: 'center',
              }}>
                <FileTextIcon size={20} color={colors.accent} />
              </View>
              <Text
                fontSize={16}
                fontWeight="600"
                color={colors.text}
                fontFamily="$body"
              >
                Notes
              </Text>
            </XStack>
            <Text
              color={colors.text}
              fontSize={14}
              fontFamily="$body"
              lineHeight={20}
            >
              {bill.notes}
            </Text>
          </Card>
        )}
      </ScrollView>

      {/* Image Zoom Modal */}
      {bill?.imageUrl && (
        <ImageZoomModal
          visible={imageZoomVisible}
          imageUrl={bill.imageUrl}
          onClose={() => setImageZoomVisible(false)}
        />
      )}
    </YStack>
  );
};

export default BillDetailScreen;
