import React, { useState } from 'react';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { RootStackParamList } from '../navigation/types';
import {
  Button,
  H2,
  XStack,
  YStack,
  Input,
  Text,
  Separator,
  Form,
  TextArea,
  Card,
} from 'tamagui';
import {
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  View,
  Alert,
  TouchableOpacity,
  Image,
  ActionSheetIOS
} from 'react-native';
import * as ImagePicker from 'expo-image-picker';
import { Measurement } from '../types';
import { colors, borderRadius, spacing } from '../theme/colors';
import { addMeasurement } from '../services/measurementService';
import { uploadImage } from '../services/storageService';
import {
  UserIcon,
  PhoneIcon,
  BillIcon,
  CalendarIcon,
  MoneyIcon,
  FileTextIcon,
  SaveIcon,
  CameraIcon,
  ImageIcon,
  ZoomIcon
} from '../components/TabIcons';
import ImageZoomModal from '../components/ImageZoomModal';
import DatePickerInput from '../components/DatePickerInput';

type Props = NativeStackScreenProps<RootStackParamList, 'AddBill'>;

const AddBillScreen = ({ navigation, route }: Props) => {
  const [customerName, setCustomerName] = useState('');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [billNumber, setBillNumber] = useState('');
  const [amount, setAmount] = useState('');
  const [deliveryDate, setDeliveryDate] = useState('');
  const [notes, setNotes] = useState('');
  const [loading, setLoading] = useState(false);
  const [imageUri, setImageUri] = useState<string | null>(null);
  const [uploadingImage, setUploadingImage] = useState(false);
  const [imageZoomVisible, setImageZoomVisible] = useState(false);

  // Request camera permissions
  React.useEffect(() => {
    (async () => {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission needed', 'Sorry, we need camera roll permissions to upload images.');
      }
    })();
  }, []);

  const pickImage = async () => {
    const showImagePicker = () => {
      if (Platform.OS === 'ios') {
        ActionSheetIOS.showActionSheetWithOptions(
          {
            options: ['Cancel', 'Take Photo', 'Choose from Gallery'],
            cancelButtonIndex: 0,
          },
          (buttonIndex) => {
            if (buttonIndex === 1) {
              openCamera();
            } else if (buttonIndex === 2) {
              openGallery();
            }
          }
        );
      } else {
        Alert.alert(
          'Select Image',
          'Choose an option',
          [
            { text: 'Cancel', style: 'cancel' },
            { text: 'Take Photo', onPress: openCamera },
            { text: 'Choose from Gallery', onPress: openGallery },
          ]
        );
      }
    };

    const openCamera = async () => {
      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission needed', 'Camera permission is required to take photos.');
        return;
      }

      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        setImageUri(result.assets[0].uri);
      }
    };

    const openGallery = async () => {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        setImageUri(result.assets[0].uri);
      }
    };

    showImagePicker();
  };

  const validateForm = () => {
    if (!customerName.trim()) {
      Alert.alert('Validation Error', 'Customer name is required');
      return false;
    }
    if (!phoneNumber.trim()) {
      Alert.alert('Validation Error', 'Phone number is required');
      return false;
    }
    if (!billNumber.trim()) {
      Alert.alert('Validation Error', 'Bill number is required');
      return false;
    }
    if (!amount.trim() || isNaN(parseFloat(amount))) {
      Alert.alert('Validation Error', 'Please enter a valid amount');
      return false;
    }
    if (!deliveryDate.trim()) {
      Alert.alert('Validation Error', 'Delivery date is required');
      return false;
    }
    return true;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    setLoading(true);
    try {
      let uploadedImageUrl = '';

      // Upload image if selected
      if (imageUri) {
        setUploadingImage(true);
        try {
          uploadedImageUrl = await uploadImage(imageUri);
        } catch (imageError) {
          console.error('Error uploading image:', imageError);
          Alert.alert('Warning', 'Failed to upload image, but bill will be created without it.');
        } finally {
          setUploadingImage(false);
        }
      }

      // Create a new measurement record with bill information
      // Format date as DD-MM-YYYY to match the expected format in HomeScreen
      const currentDate = new Date();
      const formattedDate = `${currentDate.getDate().toString().padStart(2, '0')}-${(currentDate.getMonth() + 1).toString().padStart(2, '0')}-${currentDate.getFullYear()}`;

      const newBill: Partial<Measurement> = {
        name: customerName.trim(),
        phoneNo: phoneNumber.trim(),
        billNo: billNumber,
        date: formattedDate,
        deliveryDate: deliveryDate.trim(),
        imageUrl: uploadedImageUrl,
        measurements: {}, // Empty measurements object for now
        notes: notes.trim(),
      };

      await addMeasurement(newBill);

      Alert.alert(
        'Success',
        'Bill created successfully!',
        [
          {
            text: 'OK',
            onPress: () => navigation.goBack()
          }
        ]
      );
    } catch (error) {
      console.error('Error creating bill:', error);
      Alert.alert('Error', 'Failed to create bill. Please try again.');
    } finally {
      setLoading(false);
      setUploadingImage(false);
    }
  };

  return (
    <YStack f={1} backgroundColor={colors.background}>
      {/* Clean Header */}
      <YStack
        backgroundColor={colors.card}
        paddingHorizontal={spacing.lg}
        paddingTop={spacing.xl}
        paddingBottom={spacing.md}
        borderBottomWidth={1}
        borderBottomColor={colors.border}
      >
        <XStack alignItems="center" justifyContent="space-between" marginBottom="$3">
          <YStack>
            <H2
              color={colors.text}
              fontSize={24}
              fontWeight="700"
              fontFamily="$heading"
              marginBottom="$1"
            >
              Add New Bill
            </H2>
            <Text
              color={colors.textSecondary}
              fontSize={14}
              fontFamily="$body"
            >
              Create a billing record for your customer
            </Text>
          </YStack>
          <TouchableOpacity
            onPress={() => navigation.goBack()}
            style={{
              width: 32,
              height: 32,
              borderRadius: 16,
              backgroundColor: colors.backgroundAlt,
              justifyContent: 'center',
              alignItems: 'center',
            }}
          >
            <Text fontSize={18} color={colors.textSecondary}>✕</Text>
          </TouchableOpacity>
        </XStack>
      </YStack>

      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={{ flex: 1 }}
      >
        <ScrollView
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{ paddingBottom: spacing.xl }}
        >

          {/* Image Upload Section */}
          <Card
            backgroundColor={colors.card}
            borderRadius={borderRadius.medium}
            padding={spacing.md}
            marginHorizontal={spacing.lg}
            marginTop={spacing.md}
            borderWidth={1}
            borderColor={colors.border}
          >
            <Text
              fontSize={16}
              fontWeight="600"
              color={colors.text}
              fontFamily="$body"
              marginBottom="$3"
            >
              Bill Image
            </Text>

            {imageUri ? (
              <YStack space="$3">
                <TouchableOpacity
                  onPress={() => setImageZoomVisible(true)}
                  activeOpacity={0.8}
                >
                  <Image
                    source={{ uri: imageUri }}
                    style={{
                      width: '100%',
                      height: 200,
                      borderRadius: borderRadius.medium,
                      backgroundColor: colors.backgroundAlt,
                    }}
                    resizeMode="cover"
                  />
                  <View
                    style={{
                      position: 'absolute',
                      top: 8,
                      right: 8,
                      backgroundColor: 'rgba(0, 0, 0, 0.6)',
                      borderRadius: 12,
                      padding: 6,
                    }}
                  >
                    <ZoomIcon size={14} color="white" />
                  </View>
                </TouchableOpacity>
                <XStack space="$2">
                  <Button
                    flex={1}
                    onPress={pickImage}
                    style={{
                      backgroundColor: colors.backgroundAlt,
                      borderColor: colors.border,
                      borderWidth: 1,
                    }}
                  >
                    <XStack alignItems="center" space="$2">
                      <CameraIcon size={16} color={colors.textSecondary} />
                      <Text style={{ color: colors.textSecondary, fontSize: 14 }}>Change</Text>
                    </XStack>
                  </Button>
                  <Button
                    flex={1}
                    onPress={() => setImageUri(null)}
                    style={{
                      backgroundColor: colors.backgroundAlt,
                      borderColor: colors.border,
                      borderWidth: 1,
                    }}
                  >
                    <Text style={{ color: colors.textSecondary, fontSize: 14 }}>Remove</Text>
                  </Button>
                </XStack>
              </YStack>
            ) : (
              <TouchableOpacity
                onPress={pickImage}
                style={{
                  height: 120,
                  borderRadius: borderRadius.medium,
                  borderWidth: 2,
                  borderColor: colors.border,
                  borderStyle: 'dashed',
                  backgroundColor: colors.backgroundAlt,
                  justifyContent: 'center',
                  alignItems: 'center',
                }}
              >
                <YStack alignItems="center" space="$2">
                  <CameraIcon size={32} color={colors.textTertiary} />
                  <Text color={colors.textSecondary} fontSize={14} textAlign="center">
                    Tap to add bill image
                  </Text>
                  <Text color={colors.textTertiary} fontSize={12} textAlign="center">
                    Take photo or choose from gallery
                  </Text>
                </YStack>
              </TouchableOpacity>
            )}
          </Card>

          {/* Form Fields */}
          <YStack space="$4" padding={spacing.lg}>
            {/* Customer Name */}
            <YStack space="$2">
              <Text
                fontSize={14}
                fontWeight="600"
                color={colors.text}
                fontFamily="$body"
              >
                Customer Name *
              </Text>
              <Input
                placeholder="Enter customer name"
                value={customerName}
                onChangeText={setCustomerName}
                backgroundColor={colors.card}
                borderColor={colors.border}
                borderRadius={borderRadius.input}
                paddingHorizontal={spacing.md}
                paddingVertical={spacing.sm}
                fontSize={16}
                focusStyle={{
                  borderColor: colors.primary,
                }}
              />
            </YStack>

            {/* Phone Number */}
            <YStack space="$2">
              <Text
                fontSize={14}
                fontWeight="600"
                color={colors.text}
                fontFamily="$body"
              >
                Phone Number *
              </Text>
              <Input
                placeholder="Enter phone number"
                value={phoneNumber}
                onChangeText={setPhoneNumber}
                keyboardType="phone-pad"
                backgroundColor={colors.card}
                borderColor={colors.border}
                borderRadius={borderRadius.input}
                paddingHorizontal={spacing.md}
                paddingVertical={spacing.sm}
                fontSize={16}
                focusStyle={{
                  borderColor: colors.primary,
                }}
              />
            </YStack>

            {/* Bill Number */}
            <YStack space="$2">
              <Text
                fontSize={14}
                fontWeight="600"
                color={colors.text}
                fontFamily="$body"
              >
                Bill Number *
              </Text>
              <Input
                placeholder="Enter bill number"
                value={billNumber}
                onChangeText={setBillNumber}
                backgroundColor={colors.card}
                borderColor={colors.border}
                borderRadius={borderRadius.input}
                paddingHorizontal={spacing.md}
                paddingVertical={spacing.sm}
                fontSize={16}
                focusStyle={{
                  borderColor: colors.primary,
                }}
              />
            </YStack>

            {/* Amount */}
            <YStack space="$2">
              <Text
                fontSize={14}
                fontWeight="600"
                color={colors.text}
                fontFamily="$body"
              >
                Amount *
              </Text>
              <Input
                placeholder="Enter amount"
                value={amount}
                onChangeText={setAmount}
                keyboardType="numeric"
                backgroundColor={colors.card}
                borderColor={colors.border}
                borderRadius={borderRadius.input}
                paddingHorizontal={spacing.md}
                paddingVertical={spacing.sm}
                fontSize={16}
                focusStyle={{
                  borderColor: colors.primary,
                }}
              />
            </YStack>

            {/* Delivery Date */}
            <DatePickerInput
              label="Delivery Date"
              value={deliveryDate}
              onDateChange={setDeliveryDate}
              placeholder="Select delivery date"
              required={true}
            />

            {/* Notes */}
            <YStack space="$2">
              <Text
                fontSize={14}
                fontWeight="600"
                color={colors.text}
                fontFamily="$body"
              >
                Notes
              </Text>
              <TextArea
                placeholder="Additional notes (optional)"
                value={notes}
                onChangeText={setNotes}
                numberOfLines={4}
                backgroundColor={colors.card}
                borderColor={colors.border}
                borderRadius={borderRadius.input}
                paddingHorizontal={spacing.md}
                paddingVertical={spacing.sm}
                fontSize={16}
                minHeight={100}
                textAlignVertical="top"
                focusStyle={{
                  borderColor: colors.primary,
                }}
              />
            </YStack>

            {/* Action Buttons */}
            <YStack space="$3" marginTop="$6">
              <Button
                onPress={handleSubmit}
                disabled={loading || uploadingImage}
                style={{
                  backgroundColor: colors.primary,
                  borderRadius: borderRadius.button,
                  paddingVertical: spacing.md,
                  height: 50,
                }}
                pressStyle={{
                  backgroundColor: colors.primaryDark,
                  scale: 0.98,
                }}
                opacity={loading || uploadingImage ? 0.6 : 1}
              >
                <XStack alignItems="center" space="$2">
                  <SaveIcon size={18} color="white" />
                  <Text
                    style={{
                      color: 'white',
                      fontSize: 16,
                      fontWeight: '600',
                    }}
                  >
                    {uploadingImage ? 'Uploading Image...' : loading ? 'Creating Bill...' : 'Create Bill'}
                  </Text>
                </XStack>
              </Button>

              <Button
                onPress={() => navigation.goBack()}
                style={{
                  backgroundColor: 'transparent',
                  borderColor: colors.border,
                  borderWidth: 1,
                  borderRadius: borderRadius.button,
                  paddingVertical: spacing.md,
                  height: 50,
                }}
                pressStyle={{
                  backgroundColor: colors.backgroundAlt,
                  scale: 0.98,
                }}
              >
                <Text
                  style={{
                    color: colors.text,
                    fontSize: 16,
                    fontWeight: '500',
                  }}
                >
                  Cancel
                </Text>
              </Button>
            </YStack>
          </YStack>
        </ScrollView>
      </KeyboardAvoidingView>

      {/* Image Zoom Modal */}
      {imageUri && (
        <ImageZoomModal
          visible={imageZoomVisible}
          imageUrl={imageUri}
          onClose={() => setImageZoomVisible(false)}
        />
      )}
    </YStack>
  );
};

export default AddBillScreen;
