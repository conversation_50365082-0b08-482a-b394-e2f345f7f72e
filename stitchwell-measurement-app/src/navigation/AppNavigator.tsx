import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { RootStackParamList } from './types';
import { HomeIcon, UserIcon, SettingsIcon } from '../components/TabIcons';
import { useTheme } from 'tamagui';
import { colors, shadows, borderRadius } from '../theme/colors';

// Screens
import HomeScreen from '../screens/HomeScreen';
import ProfileScreen from '../screens/ProfileScreen';
import SettingsScreen from '../screens/SettingsScreen';
import AddBillingScreen from '../screens/AddBillingScreen';
import AddBillScreen from '../screens/AddBillScreen';
import BillDetailScreen from '../screens/BillDetailScreen';
import EditBillScreen from '../screens/EditBillScreen';

const Stack = createNativeStackNavigator<RootStackParamList>();
const Tab = createBottomTabNavigator<RootStackParamList>();

const TabNavigator = () => {
  return (
    <Tab.Navigator
      screenOptions={{
        tabBarActiveTintColor: colors.primary,
        tabBarInactiveTintColor: colors.textTertiary,
        tabBarStyle: {
          backgroundColor: colors.card,
          borderTopColor: colors.border,
          height: 60,
          paddingBottom: 8,
          paddingTop: 8,
          ...shadows.small,
        },
        headerStyle: {
          backgroundColor: colors.card,
          ...shadows.small,
          elevation: 0,
          borderBottomWidth: 1,
          borderBottomColor: colors.border,
        },
        headerTitleStyle: {
          color: colors.primary,
          fontWeight: '600',
        },
      }}
    >
      <Tab.Screen
        name="Home"
        component={HomeScreen}
        options={{
          title: 'Home',
          headerShown: false,
          tabBarIcon: ({ color, size }) => <HomeIcon size={size} color={color} />
        }}
      />
      <Tab.Screen
        name="Profile"
        component={ProfileScreen}
        options={{
          title: 'Profile',
          headerShown: true,
          tabBarIcon: ({ color, size }) => <UserIcon size={size} color={color} />
        }}
      />
      <Tab.Screen
        name="Settings"
        component={SettingsScreen}
        options={{
          title: 'Settings',
          headerShown: true,
          tabBarIcon: ({ color, size }) => <SettingsIcon size={size} color={color} />
        }}
      />
    </Tab.Navigator>
  );
};

const MainStack = () => {
  return (
    <Stack.Navigator>
      <Stack.Screen
        name="TabNavigator"
        component={TabNavigator}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name="AddBilling"
        component={AddBillingScreen}
        options={{
          title: 'Add Billing Record',
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="AddBill"
        component={AddBillScreen}
        options={{
          title: 'Add New Bill',
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="BillDetail"
        component={BillDetailScreen}
        options={{
          title: 'Bill Details',
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="EditBill"
        component={EditBillScreen}
        options={{
          title: 'Edit Bill',
          headerShown: false,
        }}
      />
    </Stack.Navigator>
  );
};

const AppNavigator = () => {
  return (
    <NavigationContainer>
      <MainStack />
    </NavigationContainer>
  );
};

export default AppNavigator;
