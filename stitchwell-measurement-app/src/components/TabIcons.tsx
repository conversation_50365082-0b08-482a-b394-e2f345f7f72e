import React from 'react';
import { View, Text } from 'react-native';

interface IconProps {
  size?: number;
  color?: string;
  opacity?: number;
  marginRight?: string;
}

export const HomeIcon: React.FC<IconProps> = ({ size = 24, color = 'black' }) => {
  return (
    <View style={{ width: size, height: size, justifyContent: 'center', alignItems: 'center' }}>
      <Text style={{ fontSize: size * 0.8, color }}>🏠</Text>
    </View>
  );
};

export const UserIcon: React.FC<IconProps> = ({ size = 24, color = 'black', opacity = 1 }) => {
  return (
    <View style={{ width: size, height: size, justifyContent: 'center', alignItems: 'center', opacity }}>
      <Text style={{ fontSize: size * 0.8, color }}>👤</Text>
    </View>
  );
};

export const SettingsIcon: React.FC<IconProps> = ({ size = 24, color = 'black' }) => {
  return (
    <View style={{ width: size, height: size, justifyContent: 'center', alignItems: 'center' }}>
      <Text style={{ fontSize: size * 0.8, color }}>⚙️</Text>
    </View>
  );
};

export const SearchIcon: React.FC<IconProps> = ({ size = 24, color = 'black', opacity = 1, marginRight }) => {
  return (
    <View style={{ width: size, height: size, justifyContent: 'center', alignItems: 'center', opacity, marginRight }}>
      <Text style={{ fontSize: size * 0.8, color }}>🔍</Text>
    </View>
  );
};

export const CalendarIcon: React.FC<IconProps> = ({ size = 24, color = 'black', opacity = 1 }) => {
  return (
    <View style={{ width: size, height: size, justifyContent: 'center', alignItems: 'center', opacity }}>
      <Text style={{ fontSize: size * 0.8, color }}>📅</Text>
    </View>
  );
};

export const PhoneIcon: React.FC<IconProps> = ({ size = 24, color = 'black', opacity = 1 }) => {
  return (
    <View style={{ width: size, height: size, justifyContent: 'center', alignItems: 'center', opacity }}>
      <Text style={{ fontSize: size * 0.8, color }}>📱</Text>
    </View>
  );
};

export const FileTextIcon: React.FC<IconProps> = ({ size = 24, color = 'black', opacity = 1 }) => {
  return (
    <View style={{ width: size, height: size, justifyContent: 'center', alignItems: 'center', opacity }}>
      <Text style={{ fontSize: size * 0.8, color }}>📄</Text>
    </View>
  );
};

export const DollarSignIcon: React.FC<IconProps> = ({ size = 24, color = 'black', opacity = 1 }) => {
  return (
    <View style={{ width: size, height: size, justifyContent: 'center', alignItems: 'center', opacity }}>
      <Text style={{ fontSize: size * 0.8, color }}>💲</Text>
    </View>
  );
};

export const FilterIcon: React.FC<IconProps> = ({ size = 24, color = 'black' }) => {
  return (
    <View style={{ width: size, height: size, justifyContent: 'center', alignItems: 'center' }}>
      <Text style={{ fontSize: size * 0.8, color }}>🔽</Text>
    </View>
  );
};

export const MailIcon: React.FC<IconProps> = ({ size = 24, color = 'black' }) => {
  return (
    <View style={{ width: size, height: size, justifyContent: 'center', alignItems: 'center' }}>
      <Text style={{ fontSize: size * 0.8, color }}>✉️</Text>
    </View>
  );
};

export const MapPinIcon: React.FC<IconProps> = ({ size = 24, color = 'black' }) => {
  return (
    <View style={{ width: size, height: size, justifyContent: 'center', alignItems: 'center' }}>
      <Text style={{ fontSize: size * 0.8, color }}>📍</Text>
    </View>
  );
};

export const MoonIcon: React.FC<IconProps> = ({ size = 24, color = 'black' }) => {
  return (
    <View style={{ width: size, height: size, justifyContent: 'center', alignItems: 'center' }}>
      <Text style={{ fontSize: size * 0.8, color }}>🌙</Text>
    </View>
  );
};

export const BellIcon: React.FC<IconProps> = ({ size = 24, color = 'black' }) => {
  return (
    <View style={{ width: size, height: size, justifyContent: 'center', alignItems: 'center' }}>
      <Text style={{ fontSize: size * 0.8, color }}>🔔</Text>
    </View>
  );
};

export const GlobeIcon: React.FC<IconProps> = ({ size = 24, color = 'black' }) => {
  return (
    <View style={{ width: size, height: size, justifyContent: 'center', alignItems: 'center' }}>
      <Text style={{ fontSize: size * 0.8, color }}>🌎</Text>
    </View>
  );
};

export const LockIcon: React.FC<IconProps> = ({ size = 24, color = 'black' }) => {
  return (
    <View style={{ width: size, height: size, justifyContent: 'center', alignItems: 'center' }}>
      <Text style={{ fontSize: size * 0.8, color }}>🔒</Text>
    </View>
  );
};

export const HelpCircleIcon: React.FC<IconProps> = ({ size = 24, color = 'black' }) => {
  return (
    <View style={{ width: size, height: size, justifyContent: 'center', alignItems: 'center' }}>
      <Text style={{ fontSize: size * 0.8, color }}>❓</Text>
    </View>
  );
};

export const PlusIcon: React.FC<IconProps> = ({ size = 24, color = 'white' }) => {
  return (
    <View style={{ width: size, height: size, justifyContent: 'center', alignItems: 'center' }}>
      <Text style={{
        fontSize: size * 1.3,
        color,
        fontWeight: '700',
        textAlign: 'center',
        lineHeight: size * 1.3,
      }}>+</Text>
    </View>
  );
};

export const ChartIcon: React.FC<IconProps> = ({ size = 24, color = 'black', opacity = 1 }) => {
  return (
    <View style={{ width: size, height: size, justifyContent: 'center', alignItems: 'center', opacity }}>
      <Text style={{ fontSize: size * 0.8, color }}>📊</Text>
    </View>
  );
};

export const TrendingUpIcon: React.FC<IconProps> = ({ size = 24, color = 'black', opacity = 1 }) => {
  return (
    <View style={{ width: size, height: size, justifyContent: 'center', alignItems: 'center', opacity }}>
      <Text style={{ fontSize: size * 0.8, color }}>📈</Text>
    </View>
  );
};

export const CounterIcon: React.FC<IconProps> = ({ size = 24, color = 'black', opacity = 1 }) => {
  return (
    <View style={{ width: size, height: size, justifyContent: 'center', alignItems: 'center', opacity }}>
      <Text style={{ fontSize: size * 0.8, color }}>🔢</Text>
    </View>
  );
};

export const TrashIcon: React.FC<IconProps> = ({ size = 24, color = 'black', opacity = 1 }) => {
  return (
    <View style={{ width: size, height: size, justifyContent: 'center', alignItems: 'center', opacity }}>
      <Text style={{ fontSize: size * 0.8, color }}>🗑️</Text>
    </View>
  );
};

export const CallIcon: React.FC<IconProps> = ({ size = 24, color = 'black', opacity = 1 }) => {
  return (
    <View style={{ width: size, height: size, justifyContent: 'center', alignItems: 'center', opacity }}>
      <Text style={{ fontSize: size * 0.8, color }}>📞</Text>
    </View>
  );
};

export const BillIcon: React.FC<IconProps> = ({ size = 24, color = 'black', opacity = 1 }) => {
  return (
    <View style={{ width: size, height: size, justifyContent: 'center', alignItems: 'center', opacity }}>
      <Text style={{ fontSize: size * 0.8, color }}>🧾</Text>
    </View>
  );
};

export const ReceiptIcon: React.FC<IconProps> = ({ size = 24, color = 'black', opacity = 1 }) => {
  return (
    <View style={{ width: size, height: size, justifyContent: 'center', alignItems: 'center', opacity }}>
      <Text style={{ fontSize: size * 0.8, color }}>🧾</Text>
    </View>
  );
};

export const MoneyIcon: React.FC<IconProps> = ({ size = 24, color = 'black', opacity = 1 }) => {
  return (
    <View style={{ width: size, height: size, justifyContent: 'center', alignItems: 'center', opacity }}>
      <Text style={{ fontSize: size * 0.8, color }}>💰</Text>
    </View>
  );
};

export const SaveIcon: React.FC<IconProps> = ({ size = 24, color = 'black', opacity = 1 }) => {
  return (
    <View style={{ width: size, height: size, justifyContent: 'center', alignItems: 'center', opacity }}>
      <Text style={{ fontSize: size * 0.8, color }}>💾</Text>
    </View>
  );
};

export const CheckIcon: React.FC<IconProps> = ({ size = 24, color = 'black', opacity = 1 }) => {
  return (
    <View style={{ width: size, height: size, justifyContent: 'center', alignItems: 'center', opacity }}>
      <Text style={{ fontSize: size * 0.8, color }}>✅</Text>
    </View>
  );
};

export const CameraIcon: React.FC<IconProps> = ({ size = 24, color = 'black', opacity = 1 }) => {
  return (
    <View style={{ width: size, height: size, justifyContent: 'center', alignItems: 'center', opacity }}>
      <Text style={{ fontSize: size * 0.8, color }}>📷</Text>
    </View>
  );
};

export const ImageIcon: React.FC<IconProps> = ({ size = 24, color = 'black', opacity = 1 }) => {
  return (
    <View style={{ width: size, height: size, justifyContent: 'center', alignItems: 'center', opacity }}>
      <Text style={{ fontSize: size * 0.8, color }}>🖼️</Text>
    </View>
  );
};

export const UploadIcon: React.FC<IconProps> = ({ size = 24, color = 'black', opacity = 1 }) => {
  return (
    <View style={{ width: size, height: size, justifyContent: 'center', alignItems: 'center', opacity }}>
      <Text style={{ fontSize: size * 0.8, color }}>⬆️</Text>
    </View>
  );
};

export const EditIcon: React.FC<IconProps> = ({ size = 24, color = 'black', opacity = 1 }) => {
  return (
    <View style={{ width: size, height: size, justifyContent: 'center', alignItems: 'center', opacity }}>
      <Text style={{ fontSize: size * 0.8, color }}>✏️</Text>
    </View>
  );
};

export const CloseIcon: React.FC<IconProps> = ({ size = 24, color = 'black', opacity = 1 }) => {
  return (
    <View style={{ width: size, height: size, justifyContent: 'center', alignItems: 'center', opacity }}>
      <Text style={{ fontSize: size * 0.8, color }}>✕</Text>
    </View>
  );
};

export const ZoomIcon: React.FC<IconProps> = ({ size = 24, color = 'black', opacity = 1 }) => {
  return (
    <View style={{ width: size, height: size, justifyContent: 'center', alignItems: 'center', opacity }}>
      <Text style={{ fontSize: size * 0.8, color }}>🔍</Text>
    </View>
  );
};

export const BackIcon: React.FC<IconProps> = ({ size = 24, color = 'black', opacity = 1 }) => {
  return (
    <View style={{ width: size, height: size, justifyContent: 'center', alignItems: 'center', opacity }}>
      <Text style={{ fontSize: size * 0.8, color }}>←</Text>
    </View>
  );
};

export const MoreIcon: React.FC<IconProps> = ({ size = 24, color = 'black', opacity = 1 }) => {
  return (
    <View style={{ width: size, height: size, justifyContent: 'center', alignItems: 'center', opacity }}>
      <Text style={{ fontSize: size * 0.8, color }}>⋯</Text>
    </View>
  );
};
