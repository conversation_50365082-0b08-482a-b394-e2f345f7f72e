import React from 'react';
import {
  Modal,
  View,
  TouchableOpacity,
  StyleSheet,
  StatusBar,
  SafeAreaView,
  Dimensions,
} from 'react-native';
import ImageViewer from 'react-native-image-zoom-viewer';
import { CloseIcon } from './TabIcons';
import { colors } from '../theme/colors';

interface ImageZoomModalProps {
  visible: boolean;
  imageUrl: string;
  onClose: () => void;
}

const { width, height } = Dimensions.get('window');

const ImageZoomModal: React.FC<ImageZoomModalProps> = ({
  visible,
  imageUrl,
  onClose,
}) => {
  const imageUrls = [
    {
      url: imageUrl,
      props: {
        source: imageUrl.startsWith('http') ? undefined : { uri: imageUrl },
      },
    },
  ];

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={onClose}
      statusBarTranslucent={true}
    >
      <StatusBar backgroundColor="rgba(0,0,0,0.9)" barStyle="light-content" />
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.closeButton}
            onPress={onClose}
            activeOpacity={0.7}
          >
            <View style={styles.closeButtonInner}>
              <CloseIcon size={20} color="white" />
            </View>
          </TouchableOpacity>
        </View>
        
        <View style={styles.imageContainer}>
          <ImageViewer
            imageUrls={imageUrls}
            enableSwipeDown={true}
            onSwipeDown={onClose}
            enablePreload={true}
            saveToLocalByLongPress={false}
            menuContext={{}}
            renderIndicator={() => null}
            backgroundColor="transparent"
            enableImageZoom={true}
            useNativeDriver={true}
            swipeDownThreshold={50}
            doubleClickInterval={250}
            style={styles.imageViewer}
          />
        </View>
      </SafeAreaView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.95)',
  },
  header: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 1000,
    paddingTop: StatusBar.currentHeight || 44,
    paddingHorizontal: 20,
    paddingBottom: 10,
  },
  closeButton: {
    alignSelf: 'flex-end',
    padding: 8,
  },
  closeButtonInner: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  imageContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  imageViewer: {
    width,
    height,
  },
});

export default ImageZoomModal;
