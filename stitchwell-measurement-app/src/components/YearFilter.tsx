import React, { useState } from 'react';
import { TouchableOpacity, Modal, FlatList } from 'react-native';
import { YStack, XStack, Text, Button } from 'tamagui';
import { CalendarIcon, ChevronDownIcon, CheckIcon } from './TabIcons';
import { colors, borderRadius, spacing, shadows } from '../theme/colors';

interface YearFilterProps {
  selectedYear: number | null;
  onYearChange: (year: number | null) => void;
  availableYears: number[];
  disabled?: boolean;
}

const YearFilter: React.FC<YearFilterProps> = ({
  selectedYear,
  onYearChange,
  availableYears,
  disabled = false,
}) => {
  const [isModalVisible, setIsModalVisible] = useState(false);

  const openModal = () => {
    if (!disabled) {
      setIsModalVisible(true);
    }
  };

  const closeModal = () => {
    setIsModalVisible(false);
  };

  const selectYear = (year: number | null) => {
    onYearChange(year);
    closeModal();
  };

  const renderYearItem = ({ item }: { item: number | null }) => {
    const isSelected = selectedYear === item;
    const displayText = item === null ? 'All Years' : item.toString();
    
    return (
      <TouchableOpacity
        onPress={() => selectYear(item)}
        style={{
          paddingHorizontal: spacing.lg,
          paddingVertical: spacing.md,
          backgroundColor: isSelected ? `${colors.primary}15` : 'transparent',
          borderBottomWidth: 1,
          borderBottomColor: colors.border,
        }}
        activeOpacity={0.7}
      >
        <XStack alignItems="center" justifyContent="space-between">
          <Text
            fontSize={16}
            fontFamily="$body"
            fontWeight={isSelected ? "600" : "400"}
            color={isSelected ? colors.primary : colors.text}
          >
            {displayText}
          </Text>
          {isSelected && (
            <CheckIcon size={20} color={colors.primary} />
          )}
        </XStack>
      </TouchableOpacity>
    );
  };

  // Prepare data with "All Years" option
  const yearOptions = [null, ...availableYears.sort((a, b) => b - a)];

  return (
    <>
      <TouchableOpacity
        onPress={openModal}
        disabled={disabled}
        style={{
          backgroundColor: 'rgba(255, 255, 255, 0.15)',
          borderRadius: borderRadius.button,
          paddingHorizontal: spacing.md,
          paddingVertical: spacing.sm,
          minWidth: 100,
          opacity: disabled ? 0.5 : 1,
          ...shadows.small,
        }}
        activeOpacity={0.7}
      >
        <XStack alignItems="center" justifyContent="space-between" gap="$2">
          <XStack alignItems="center" gap="$2">
            <CalendarIcon size={16} color="rgba(255, 255, 255, 0.8)" />
            <Text
              fontSize={14}
              fontFamily="$body"
              fontWeight="500"
              color="rgba(255, 255, 255, 0.9)"
              numberOfLines={1}
            >
              {selectedYear ? selectedYear.toString() : 'All Years'}
            </Text>
          </XStack>
          <ChevronDownIcon size={14} color="rgba(255, 255, 255, 0.6)" />
        </XStack>
      </TouchableOpacity>

      <Modal
        visible={isModalVisible}
        transparent
        animationType="fade"
        onRequestClose={closeModal}
      >
        <TouchableOpacity
          style={{
            flex: 1,
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            justifyContent: 'center',
            alignItems: 'center',
            paddingHorizontal: spacing.lg,
          }}
          activeOpacity={1}
          onPress={closeModal}
        >
          <TouchableOpacity
            activeOpacity={1}
            style={{
              backgroundColor: colors.card,
              borderRadius: borderRadius.card,
              width: '100%',
              maxWidth: 300,
              maxHeight: 400,
              ...shadows.large,
            }}
            onPress={(e) => e.stopPropagation()}
          >
            <YStack>
              {/* Header */}
              <XStack
                alignItems="center"
                justifyContent="space-between"
                paddingHorizontal={spacing.lg}
                paddingVertical={spacing.md}
                borderBottomWidth={1}
                borderBottomColor={colors.border}
              >
                <Text
                  fontSize={18}
                  fontFamily="$heading"
                  fontWeight="600"
                  color={colors.text}
                >
                  Select Year
                </Text>
                <Button
                  size="$3"
                  circular
                  backgroundColor="transparent"
                  color={colors.textSecondary}
                  onPress={closeModal}
                >
                  ✕
                </Button>
              </XStack>

              {/* Year List */}
              <FlatList
                data={yearOptions}
                renderItem={renderYearItem}
                keyExtractor={(item) => item?.toString() || 'all'}
                showsVerticalScrollIndicator={false}
                style={{
                  maxHeight: 300,
                }}
              />
            </YStack>
          </TouchableOpacity>
        </TouchableOpacity>
      </Modal>
    </>
  );
};

export default YearFilter;
