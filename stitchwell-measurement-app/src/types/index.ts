export interface BillingRecord {
  id: string;
  billNumber: string;
  customerName: string;
  contactNumber: string;
  date: string;
  amount: number;
  status: 'paid' | 'pending' | 'overdue';
}

export interface Measurement {
  id: string;
  name: string;
  phoneNo: string;
  billNo: string;
  date: string;
  deliveryDate: string;
  imageUrl: string;
  measurements?: {
    chest?: number;
    waist?: number;
    hip?: number;
    shoulder?: number;
    sleeve?: number;
    neck?: number;
    inseam?: number;
    thigh?: number;
    outseam?: number;
    [key: string]: number | undefined;
  };
  notes?: string;
  timestamp?: number; // Add timestamp for proper sorting
}
