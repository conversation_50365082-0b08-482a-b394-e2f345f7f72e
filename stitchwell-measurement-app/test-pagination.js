// Quick test script to verify pagination is working
import { initializeApp } from 'firebase/app';
import { getFirestore, collection, query, orderBy, limit, getDocs } from 'firebase/firestore';

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyCJ11B1BATse018MOQ1NTKQY45zY6fg-SU",
  authDomain: "tailor-dd259.firebaseapp.com",
  projectId: "tailor-dd259",
  storageBucket: "tailor-dd259.appspot.com",
  messagingSenderId: "847981940747",
  appId: "1:847981940747:android:eb82f08a16c340f5f0b70e"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

const MEASUREMENTS_COLLECTION = 'measurements';

// Test pagination query
async function testPagination() {
  try {
    console.log('🧪 Testing pagination query...');
    
    const measurementsRef = collection(db, MEASUREMENTS_COLLECTION);
    
    // Test query with orderBy timestamp and limit
    const testQuery = query(
      measurementsRef,
      orderBy('timestamp', 'desc'),
      limit(15)
    );

    const querySnapshot = await getDocs(testQuery);
    
    console.log(`✅ Pagination test successful!`);
    console.log(`📊 Retrieved ${querySnapshot.size} measurements`);
    
    if (!querySnapshot.empty) {
      const firstDoc = querySnapshot.docs[0];
      const data = firstDoc.data();
      console.log(`📝 First measurement:`, {
        id: firstDoc.id,
        name: data.name,
        date: data.date,
        timestamp: data.timestamp,
        timestampDate: new Date(data.timestamp).toLocaleDateString()
      });
    }
    
  } catch (error) {
    console.error('❌ Pagination test failed:', error);
  }
}

// Run the test
testPagination().then(() => {
  console.log('🏁 Pagination test completed');
  process.exit(0);
}).catch((error) => {
  console.error('💥 Pagination test failed:', error);
  process.exit(1);
});
